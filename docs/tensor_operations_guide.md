# Qilin Tensor Operations Guide

This guide provides comprehensive examples and usage patterns for the Qilin inference engine's tensor operations.

## Table of Contents

1. [Basic Tensor Operations](#basic-tensor-operations)
2. [Reduction Operations](#reduction-operations)
3. [Advanced Operations](#advanced-operations)
4. [In-place Operations](#in-place-operations)
5. [SIMD Optimizations](#simd-optimizations)
6. [Parallel Computing](#parallel-computing)
7. [Performance Optimization](#performance-optimization)

## Basic Tensor Operations

### Element-wise Arithmetic

```rust
use qilin_inference::tensor::{CpuTensor, Shape, TensorOps};

// Create tensors
let data1 = vec![1.0, 2.0, 3.0, 4.0];
let data2 = vec![2.0, 3.0, 4.0, 5.0];
let tensor1 = CpuTensor::from_data(data1, Shape::new(vec![2, 2])).unwrap();
let tensor2 = CpuTensor::from_data(data2, Shape::new(vec![2, 2])).unwrap();

// Element-wise operations
let sum = tensor1.add(&tensor2).unwrap();        // [[3, 5], [7, 9]]
let diff = tensor1.sub(&tensor2).unwrap();       // [[-1, -1], [-1, -1]]
let product = tensor1.mul(&tensor2).unwrap();    // [[2, 6], [12, 20]]
let quotient = tensor1.div(&tensor2).unwrap();   // [[0.5, 0.67], [0.75, 0.8]]
```

### Scalar Operations

```rust
// Scalar arithmetic
let scaled = tensor1.mul_scalar(2.0).unwrap();   // [[2, 4], [6, 8]]
let shifted = tensor1.add_scalar(10.0).unwrap(); // [[11, 12], [13, 14]]
let normalized = tensor1.div_scalar(4.0).unwrap(); // [[0.25, 0.5], [0.75, 1.0]]
```

### Matrix Operations

```rust
// Matrix multiplication
let a = CpuTensor::from_data(vec![1.0, 2.0, 3.0, 4.0], Shape::new(vec![2, 2])).unwrap();
let b = CpuTensor::from_data(vec![5.0, 6.0, 7.0, 8.0], Shape::new(vec![2, 2])).unwrap();
let matmul_result = a.matmul(&b).unwrap();

// Dot product
let vec1 = CpuTensor::from_data(vec![1.0, 2.0, 3.0], Shape::new(vec![3])).unwrap();
let vec2 = CpuTensor::from_data(vec![4.0, 5.0, 6.0], Shape::new(vec![3])).unwrap();
let dot_product = vec1.dot(&vec2).unwrap(); // 32.0 (1*4 + 2*5 + 3*6)
```

## Reduction Operations

### Statistical Reductions

```rust
use qilin_inference::tensor::reduction::ReductionOps;

let data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0];
let tensor = CpuTensor::from_data(data, Shape::new(vec![2, 3])).unwrap();
// Tensor: [[1, 2, 3], [4, 5, 6]]

// Sum operations
let total_sum = tensor.sum_dim(&[], false).unwrap();      // 21.0
let row_sums = tensor.sum_dim(&[1], false).unwrap();      // [6, 15]
let col_sums = tensor.sum_dim(&[0], false).unwrap();      // [5, 7, 9]

// Mean operations
let overall_mean = tensor.mean_dim(&[], false).unwrap();  // 3.5
let row_means = tensor.mean_dim(&[1], false).unwrap();    // [2.0, 5.0]

// Standard deviation and variance
let std_dev = tensor.std_dim(&[], false, false).unwrap(); // Population std
let variance = tensor.var_dim(&[1], true, true).unwrap(); // Sample variance per row
```

### Extremal Operations

```rust
// Find maximum and minimum values with indices
let (max_vals, max_indices) = tensor.max_dim(1, false).unwrap();
// max_vals: [3.0, 6.0], max_indices: [2, 2]

let (min_vals, min_indices) = tensor.min_dim(0, false).unwrap();
// min_vals: [1.0, 2.0, 3.0], min_indices: [0, 0, 0]

// Get indices only
let argmax_indices = tensor.argmax(1, false).unwrap();    // [2, 2]
let argmin_indices = tensor.argmin(0, false).unwrap();    // [0, 0, 0]
```

### Logical and Norm Operations

```rust
// Logical operations
let bool_tensor = CpuTensor::from_data(vec![1.0, 0.0, 1.0, 0.0], Shape::new(vec![2, 2])).unwrap();
let all_true = bool_tensor.all(None, false).unwrap();     // false
let any_true = bool_tensor.any(Some(1), false).unwrap(); // [true, false]

// Norm calculations
let l1_norm = tensor.norm_l1(None, false).unwrap();       // 21.0
let l2_norm = tensor.norm_l2(Some(1), false).unwrap();    // [3.74, 8.77]
```

## Advanced Operations

### Sorting and Top-K

```rust
use qilin_inference::tensor::advanced::AdvancedOps;

let data = vec![3.0, 1.0, 4.0, 2.0];
let tensor = CpuTensor::from_data(data, Shape::new(vec![4])).unwrap();

// Sort tensor
let (sorted_values, sorted_indices) = tensor.sort(0, false).unwrap();
// sorted_values: [1.0, 2.0, 3.0, 4.0]
// sorted_indices: [1, 3, 0, 2]

// Get sort indices only
let argsort_indices = tensor.argsort(0, true).unwrap(); // Descending: [2, 0, 3, 1]

// Top-k values
let (top_values, top_indices) = tensor.topk(2, 0, true).unwrap();
// top_values: [4.0, 3.0], top_indices: [2, 0]
```

### Tensor Manipulation

```rust
// Concatenation
let tensor1 = CpuTensor::from_data(vec![1.0, 2.0], Shape::new(vec![1, 2])).unwrap();
let tensor2 = CpuTensor::from_data(vec![3.0, 4.0], Shape::new(vec![1, 2])).unwrap();
let concatenated = CpuTensor::cat(&[&tensor1, &tensor2], 0).unwrap(); // Shape: [2, 2]

// Stacking
let stacked = CpuTensor::stack(&[&tensor1, &tensor2], 1).unwrap(); // Shape: [1, 2, 2]

// Splitting
let large_tensor = CpuTensor::from_data(vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0], Shape::new(vec![6])).unwrap();
let chunks = large_tensor.split(2, 0).unwrap(); // 3 tensors of size 2
let equal_chunks = large_tensor.chunk(3, 0).unwrap(); // 3 equal chunks
```

### Indexing and Masking

```rust
// Gather operation
let data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0];
let tensor = CpuTensor::from_data(data, Shape::new(vec![2, 3])).unwrap();
let indices = CpuTensor::from_data(vec![0.0, 2.0, 1.0], Shape::new(vec![3])).unwrap();
let gathered = tensor.gather(1, &indices).unwrap();

// Masked operations
let mask = CpuTensor::from_data(vec![1.0, 0.0, 1.0, 0.0], Shape::new(vec![2, 2])).unwrap();
let filled = tensor.masked_fill(&mask, 99.0).unwrap();
let selected = tensor.masked_select(&mask).unwrap(); // 1D tensor with selected elements
```

## In-place Operations

```rust
use qilin_inference::tensor::inplace::InplaceOps;

let mut tensor = CpuTensor::from_data(vec![1.0, 2.0, 3.0, 4.0], Shape::new(vec![2, 2])).unwrap();

// Mathematical functions (in-place)
tensor.sin_inplace().unwrap();
tensor.exp_inplace().unwrap();
tensor.sqrt_inplace().unwrap();

// Scalar operations (in-place)
tensor.add_scalar_inplace(5.0).unwrap();
tensor.mul_scalar_inplace(2.0).unwrap();

// Element-wise operations (in-place)
let other = CpuTensor::from_data(vec![1.0, 1.0, 1.0, 1.0], Shape::new(vec![2, 2])).unwrap();
tensor.add_inplace(&other).unwrap();
tensor.mul_inplace(&other).unwrap();
```

## SIMD Optimizations

```rust
use qilin_inference::tensor::simd::SimdOps;

// SIMD operations work on raw f32 slices
let a = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0];
let b = vec![2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0];
let mut result = vec![0.0; 8];

// SIMD-optimized operations (processes 8 elements at once)
SimdOps::add_f32(&a, &b, &mut result);
SimdOps::mul_f32(&a, &b, &mut result);
let dot_product = SimdOps::dot_f32(&a, &b);
let sum = SimdOps::sum_f32(&a);
```

## Parallel Computing

```rust
use qilin_inference::tensor::parallel::{ParallelConfig, ParallelOps};

// Configure parallel execution
let config = ParallelConfig::new(
    10_000,    // parallel_threshold
    Some(4),   // max_threads
    1_000      // chunk_size
);

// Check if operation should be parallelized
let should_parallel = config.should_parallelize(50_000); // true

// Parallel operations work on large datasets
let large_a = vec![1.0; 100_000];
let large_b = vec![2.0; 100_000];
let mut result = vec![0.0; 100_000];

ParallelOps::add_f32(&large_a, &large_b, &mut result, &config);
ParallelOps::mul_f32(&large_a, &large_b, &mut result, &config);
```

## Performance Optimization

```rust
use qilin_inference::tensor::optimized::{OptimizationConfig, OptimizedOps, OptimizationStrategy};

// Create optimization configuration
let parallel_config = ParallelConfig::default();
let opt_config = OptimizationConfig::new(parallel_config, 64, true);

// Automatic strategy selection
let strategy = opt_config.optimization_strategy(50_000);
match strategy {
    OptimizationStrategy::Sequential => println!("Using sequential execution"),
    OptimizationStrategy::Simd => println!("Using SIMD optimization"),
    OptimizationStrategy::Parallel => println!("Using parallel execution"),
    OptimizationStrategy::SimdParallel => println!("Using SIMD + parallel"),
}

// Optimized operations automatically choose the best implementation
let large_tensor1 = CpuTensor::from_data(vec![1.0; 100_000], Shape::new(vec![100_000])).unwrap();
let large_tensor2 = CpuTensor::from_data(vec![2.0; 100_000], Shape::new(vec![100_000])).unwrap();

// This will automatically use the optimal strategy (likely parallel for this size)
let optimized_result = large_tensor1.add(&large_tensor2).unwrap();
```

## Performance Guidelines

### Size-based Recommendations

| Operation Type | Small (<1K) | Medium (1K-10K) | Large (10K-100K) | Very Large (>100K) |
|----------------|-------------|-----------------|-------------------|---------------------|
| Simple Arithmetic | Sequential | SIMD | Parallel | SIMD + Parallel |
| Mathematical Functions | Sequential | SIMD | SIMD | SIMD + Parallel |
| Reductions | Sequential | Sequential | Parallel | Parallel |
| Matrix Operations | Sequential | Sequential | Parallel | Parallel |

### Memory Considerations

- **In-place operations**: Use when memory is limited
- **SIMD operations**: Most effective with contiguous memory
- **Parallel operations**: Consider memory bandwidth limitations
- **Large tensors**: Monitor memory usage and consider chunking

### Benchmarking

Use the built-in benchmark suite to measure performance:

```bash
cargo bench --features benchmarks,simd
```

This will run comprehensive benchmarks across all operation types and sizes.
