//! Performance benchmarks for tensor operations.
//!
//! This benchmark suite tests the performance of various tensor operations
//! including mathematical functions, reductions, advanced operations, and
//! optimized implementations (SIMD, parallel, optimized).

#[cfg(feature = "benchmarks")]
use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};

#[cfg(feature = "benchmarks")]
use qilin_inference::tensor::{
    cpu::CpuTensor, Tensor, TensorOps, Shape, Numeric,
    simd::SimdOps,
    parallel::{ParallelOps, ParallelConfig},
    optimized::{OptimizedOps, OptimizationConfig},
    reduction::ReductionOps,
    advanced::AdvancedOps,
    inplace::InplaceOps,
};

#[cfg(feature = "benchmarks")]
/// Generate test data for benchmarks.
fn generate_test_data_f32(size: usize) -> (Vec<f32>, Vec<f32>) {
    let a: Vec<f32> = (0..size).map(|i| (i as f32) * 0.001).collect();
    let b: Vec<f32> = (0..size).map(|i| ((size - i) as f32) * 0.001).collect();
    (a, b)
}

#[cfg(feature = "benchmarks")]
/// Generate test tensor for benchmarks.
fn generate_test_tensor_f32(shape: Vec<usize>) -> CpuTensor<f32> {
    let size = shape.iter().product();
    let data: Vec<f32> = (0..size).map(|i| (i as f32) * 0.001).collect();
    CpuTensor::from_data(data, Shape::new(shape)).unwrap()
}

#[cfg(feature = "benchmarks")]
/// Benchmark basic mathematical operations.
fn bench_math_ops(c: &mut Criterion) {
    let mut group = c.benchmark_group("math_operations");
    
    for size in [1000, 10_000, 100_000].iter() {
        let tensor = generate_test_tensor_f32(vec![*size]);
        
        // Benchmark sin
        group.bench_with_input(BenchmarkId::new("sin", size), size, |b, _| {
            b.iter(|| {
                let result = tensor.sin();
                black_box(result)
            })
        });
        
        // Benchmark cos
        group.bench_with_input(BenchmarkId::new("cos", size), size, |b, _| {
            b.iter(|| {
                let result = tensor.cos();
                black_box(result)
            })
        });
        
        // Benchmark exp
        group.bench_with_input(BenchmarkId::new("exp", size), size, |b, _| {
            b.iter(|| {
                let result = tensor.exp();
                black_box(result)
            })
        });
        
        // Benchmark log
        group.bench_with_input(BenchmarkId::new("log", size), size, |b, _| {
            b.iter(|| {
                let result = tensor.log();
                black_box(result)
            })
        });
    }
    
    group.finish();
}

#[cfg(feature = "benchmarks")]
/// Benchmark element-wise operations.
fn bench_element_wise_ops(c: &mut Criterion) {
    let mut group = c.benchmark_group("element_wise_operations");
    
    for size in [1000, 10_000, 100_000].iter() {
        let tensor_a = generate_test_tensor_f32(vec![*size]);
        let tensor_b = generate_test_tensor_f32(vec![*size]);
        
        // Benchmark addition
        group.bench_with_input(BenchmarkId::new("add", size), size, |b, _| {
            b.iter(|| {
                let result = tensor_a.add(&tensor_b);
                black_box(result)
            })
        });
        
        // Benchmark multiplication
        group.bench_with_input(BenchmarkId::new("mul", size), size, |b, _| {
            b.iter(|| {
                let result = tensor_a.mul(&tensor_b);
                black_box(result)
            })
        });
        
        // Benchmark scalar addition
        group.bench_with_input(BenchmarkId::new("add_scalar", size), size, |b, _| {
            b.iter(|| {
                let result = tensor_a.add_scalar(2.5);
                black_box(result)
            })
        });
        
        // Benchmark scalar multiplication
        group.bench_with_input(BenchmarkId::new("mul_scalar", size), size, |b, _| {
            b.iter(|| {
                let result = tensor_a.mul_scalar(2.5);
                black_box(result)
            })
        });
    }
    
    group.finish();
}

#[cfg(feature = "benchmarks")]
/// Benchmark reduction operations.
fn bench_reduction_ops(c: &mut Criterion) {
    let mut group = c.benchmark_group("reduction_operations");
    
    for size in [1000, 10_000, 100_000].iter() {
        let tensor = generate_test_tensor_f32(vec![*size]);
        
        // Benchmark sum
        group.bench_with_input(BenchmarkId::new("sum", size), size, |b, _| {
            b.iter(|| {
                let result = tensor.sum(None, false);
                black_box(result)
            })
        });

        // Benchmark mean
        group.bench_with_input(BenchmarkId::new("mean", size), size, |b, _| {
            b.iter(|| {
                let result = tensor.mean(None, false);
                black_box(result)
            })
        });

        // Benchmark max
        group.bench_with_input(BenchmarkId::new("max", size), size, |b, _| {
            b.iter(|| {
                let result = tensor.max(None, false);
                black_box(result)
            })
        });

        // Benchmark min
        group.bench_with_input(BenchmarkId::new("min", size), size, |b, _| {
            b.iter(|| {
                let result = tensor.min(None, false);
                black_box(result)
            })
        });
    }
    
    // Benchmark dimensional reductions
    for &(rows, cols) in &[(100, 100), (1000, 100), (100, 1000)] {
        let tensor = generate_test_tensor_f32(vec![rows, cols]);
        let size_str = format!("{}x{}", rows, cols);
        
        group.bench_with_input(BenchmarkId::new("sum_dim_0", &size_str), &size_str, |b, _| {
            b.iter(|| {
                let result = tensor.sum(Some(0), false);
                black_box(result)
            })
        });

        group.bench_with_input(BenchmarkId::new("mean_dim_1", &size_str), &size_str, |b, _| {
            b.iter(|| {
                let result = tensor.mean(Some(1), false);
                black_box(result)
            })
        });
    }
    
    group.finish();
}

#[cfg(feature = "benchmarks")]
/// Benchmark advanced operations.
fn bench_advanced_ops(c: &mut Criterion) {
    let mut group = c.benchmark_group("advanced_operations");
    
    for size in [1000, 10_000].iter() {
        let tensor = generate_test_tensor_f32(vec![*size]);
        
        // Benchmark sort
        group.bench_with_input(BenchmarkId::new("sort", size), size, |b, _| {
            b.iter(|| {
                let result = tensor.sort(0, false);
                black_box(result)
            })
        });

        // Benchmark argsort
        group.bench_with_input(BenchmarkId::new("argsort", size), size, |b, _| {
            b.iter(|| {
                let result = tensor.argsort(0, false);
                black_box(result)
            })
        });

        // Benchmark topk
        group.bench_with_input(BenchmarkId::new("topk_10", size), size, |b, _| {
            b.iter(|| {
                let result = tensor.topk(10, 0, true);
                black_box(result)
            })
        });
    }
    
    // Benchmark split and chunk
    for &size in &[1000, 10_000] {
        let tensor = generate_test_tensor_f32(vec![size]);
        
        group.bench_with_input(BenchmarkId::new("split_10", size), &size, |b, _| {
            b.iter(|| {
                let result = tensor.split(size / 10, 0);
                black_box(result)
            })
        });

        group.bench_with_input(BenchmarkId::new("chunk_10", size), &size, |b, _| {
            b.iter(|| {
                let result = tensor.chunk(10, 0);
                black_box(result)
            })
        });
    }
    
    group.finish();
}

#[cfg(feature = "benchmarks")]
/// Benchmark in-place operations.
fn bench_inplace_ops(c: &mut Criterion) {
    let mut group = c.benchmark_group("inplace_operations");
    
    for size in [1000, 10_000, 100_000].iter() {
        // Benchmark in-place sin
        group.bench_with_input(BenchmarkId::new("sin_inplace", size), size, |b, _| {
            b.iter(|| {
                let mut tensor = generate_test_tensor_f32(vec![*size]);
                tensor.sin_inplace();
                black_box(tensor)
            })
        });

        // Benchmark in-place exp
        group.bench_with_input(BenchmarkId::new("exp_inplace", size), size, |b, _| {
            b.iter(|| {
                let mut tensor = generate_test_tensor_f32(vec![*size]);
                tensor.exp_inplace();
                black_box(tensor)
            })
        });

        // Benchmark in-place add scalar
        group.bench_with_input(BenchmarkId::new("add_scalar_inplace", size), size, |b, _| {
            b.iter(|| {
                let mut tensor = generate_test_tensor_f32(vec![*size]);
                tensor.add_scalar_inplace(1.5);
                black_box(tensor)
            })
        });
    }
    
    group.finish();
}

#[cfg(feature = "benchmarks")]
/// Benchmark SIMD operations.
#[cfg(feature = "simd")]
fn bench_simd_ops(c: &mut Criterion) {
    let mut group = c.benchmark_group("simd_operations");
    
    for size in [1000, 10_000, 100_000].iter() {
        let (a, b) = generate_test_data_f32(*size);

        // Benchmark SIMD addition
        group.bench_with_input(BenchmarkId::new("simd_add", size), size, |bench, _| {
            bench.iter(|| {
                let mut result = vec![0.0; *size];
                SimdOps::add_f32(&a, &b, &mut result);
                black_box(result)
            })
        });

        // Benchmark SIMD multiplication
        group.bench_with_input(BenchmarkId::new("simd_mul", size), size, |bench, _| {
            bench.iter(|| {
                let mut result = vec![0.0; *size];
                SimdOps::mul_f32(&a, &b, &mut result);
                black_box(result)
            })
        });
        
        // Benchmark SIMD dot product
        group.bench_with_input(BenchmarkId::new("simd_dot", size), size, |bench, _| {
            bench.iter(|| {
                let result = SimdOps::dot_product_f32(&a, &b);
                black_box(result)
            })
        });
        
        // Benchmark SIMD sum
        group.bench_with_input(BenchmarkId::new("simd_sum", size), size, |bench, _| {
            bench.iter(|| {
                let result = SimdOps::sum_f32(&a);
                black_box(result)
            })
        });
    }
    
    group.finish();
}

#[cfg(feature = "benchmarks")]
/// Benchmark parallel operations.
fn bench_parallel_ops(c: &mut Criterion) {
    let mut group = c.benchmark_group("parallel_operations");
    let config = ParallelConfig::new(1000, None, 100);
    
    for size in [10_000, 100_000, 1_000_000].iter() {
        let (a, b) = generate_test_data_f32(*size);

        // Benchmark parallel addition
        group.bench_with_input(BenchmarkId::new("parallel_add", size), size, |bench, _| {
            bench.iter(|| {
                let mut result = vec![0.0; *size];
                ParallelOps::add_f32_parallel(&a, &b, &mut result, &config);
                black_box(result)
            })
        });
        
        // Benchmark parallel dot product
        group.bench_with_input(BenchmarkId::new("parallel_dot", size), size, |bench, _| {
            bench.iter(|| {
                let result = ParallelOps::dot_product_f32_parallel(&a, &b, &config);
                black_box(result)
            })
        });
    }
    
    group.finish();
}

#[cfg(feature = "benchmarks")]
/// Benchmark optimized operations (auto-selecting best implementation).
fn bench_optimized_ops(c: &mut Criterion) {
    let mut group = c.benchmark_group("optimized_operations");
    let config = OptimizationConfig::default();
    
    for size in [1000, 10_000, 100_000].iter() {
        let (a, b) = generate_test_data_f32(*size);

        // Benchmark optimized addition
        group.bench_with_input(BenchmarkId::new("optimized_add", size), size, |bench, _| {
            bench.iter(|| {
                let mut result = vec![0.0; *size];
                OptimizedOps::add_f32_optimized(&a, &b, &mut result, &config);
                black_box(result)
            })
        });
        
        // Benchmark optimized dot product
        group.bench_with_input(BenchmarkId::new("optimized_dot", size), size, |bench, _| {
            bench.iter(|| {
                let result = OptimizedOps::dot_product_f32_optimized(&a, &b, &config);
                black_box(result)
            })
        });
    }
    
    group.finish();
}

#[cfg(feature = "benchmarks")]
/// Benchmark comparison between different implementations.
fn bench_implementation_comparison(c: &mut Criterion) {
    let mut group = c.benchmark_group("implementation_comparison");
    let size = 50_000;
    let (a, b) = generate_test_data_f32(size);

    // Sequential vs SIMD vs Parallel vs Optimized addition
    group.bench_function("sequential_add", |bench| {
        bench.iter(|| {
            let mut result = vec![0.0; size];
            for ((a_val, b_val), res) in a.iter().zip(b.iter()).zip(result.iter_mut()) {
                *res = a_val + b_val;
            }
            black_box(result)
        })
    });

    #[cfg(feature = "simd")]
    group.bench_function("simd_add", |bench| {
        bench.iter(|| {
            let mut result = vec![0.0; size];
            SimdOps::add_f32(&a, &b, &mut result);
            black_box(result)
        })
    });

    let parallel_config = ParallelConfig::new(1000, None, 100);
    group.bench_function("parallel_add", |bench| {
        bench.iter(|| {
            let mut result = vec![0.0; size];
            ParallelOps::add_f32_parallel(&a, &b, &mut result, &parallel_config);
            black_box(result)
        })
    });

    let opt_config = OptimizationConfig::default();
    group.bench_function("optimized_add", |bench| {
        bench.iter(|| {
            let mut result = vec![0.0; size];
            OptimizedOps::add_f32_optimized(&a, &b, &mut result, &opt_config);
            black_box(result)
        })
    });

    // Sequential vs SIMD vs Parallel vs Optimized dot product
    group.bench_function("sequential_dot", |bench| {
        bench.iter(|| {
            let result: f32 = a.iter().zip(b.iter()).map(|(a_val, b_val)| a_val * b_val).sum();
            black_box(result)
        })
    });

    #[cfg(feature = "simd")]
    group.bench_function("simd_dot", |bench| {
        bench.iter(|| {
            let result = SimdOps::dot_product_f32(&a, &b);
            black_box(result)
        })
    });

    group.bench_function("parallel_dot", |bench| {
        bench.iter(|| {
            let result = ParallelOps::dot_product_f32_parallel(&a, &b, &parallel_config);
            black_box(result)
        })
    });

    group.bench_function("optimized_dot", |bench| {
        bench.iter(|| {
            let result = OptimizedOps::dot_product_f32_optimized(&a, &b, &opt_config);
            black_box(result)
        })
    });

    group.finish();
}

#[cfg(feature = "benchmarks")]
/// Benchmark memory operations.
fn bench_memory_ops(c: &mut Criterion) {
    let mut group = c.benchmark_group("memory_operations");

    for size in [1000, 10_000, 100_000].iter() {
        // Benchmark tensor creation
        group.bench_with_input(BenchmarkId::new("tensor_creation", size), size, |b, &size| {
            b.iter(|| {
                let data: Vec<f32> = (0..size).map(|i| i as f32).collect();
                let tensor = CpuTensor::from_data(data, Shape::new(vec![size]));
                black_box(tensor)
            })
        });

        // Benchmark tensor clone
        let tensor = generate_test_tensor_f32(vec![*size]);
        group.bench_with_input(BenchmarkId::new("tensor_clone", size), size, |b, _| {
            b.iter(|| {
                let cloned = tensor.clone();
                black_box(cloned)
            })
        });

        // Benchmark tensor reshape
        let tensor = generate_test_tensor_f32(vec![*size]);
        if size % 10 == 0 {
            group.bench_with_input(BenchmarkId::new("tensor_reshape", size), size, |b, &size| {
                b.iter(|| {
                    let reshaped = tensor.reshape(&Shape::new(vec![size / 10, 10]));
                    black_box(reshaped)
                })
            });
        }
    }

    group.finish();
}

#[cfg(feature = "benchmarks")]
/// Benchmark matrix operations.
fn bench_matrix_ops(c: &mut Criterion) {
    let mut group = c.benchmark_group("matrix_operations");

    for &size in &[64, 128, 256] {
        let tensor_a = generate_test_tensor_f32(vec![size, size]);
        let tensor_b = generate_test_tensor_f32(vec![size, size]);

        // Benchmark matrix multiplication
        group.bench_with_input(BenchmarkId::new("matmul", size), &size, |b, _| {
            b.iter(|| {
                let result = tensor_a.matmul(&tensor_b);
                black_box(result)
            })
        });

        // Benchmark transpose
        group.bench_with_input(BenchmarkId::new("transpose", size), &size, |b, _| {
            b.iter(|| {
                let result = tensor_a.transpose(0, 1);
                black_box(result)
            })
        });
    }

    group.finish();
}

// Define benchmark groups
#[cfg(feature = "benchmarks")]
criterion_group!(
    benches,
    bench_math_ops,
    bench_element_wise_ops,
    bench_reduction_ops,
    bench_advanced_ops,
    bench_inplace_ops,
    bench_parallel_ops,
    bench_optimized_ops,
    bench_implementation_comparison,
    bench_memory_ops,
    bench_matrix_ops
);

// Add SIMD benchmarks conditionally
#[cfg(all(feature = "benchmarks", feature = "simd"))]
criterion_group!(simd_benches, bench_simd_ops);

#[cfg(all(feature = "benchmarks", feature = "simd"))]
criterion_main!(benches, simd_benches);

#[cfg(all(feature = "benchmarks", not(feature = "simd")))]
criterion_main!(benches);

#[cfg(not(feature = "benchmarks"))]
fn main() {
    println!("Benchmarks require the 'benchmarks' feature to be enabled");
}
