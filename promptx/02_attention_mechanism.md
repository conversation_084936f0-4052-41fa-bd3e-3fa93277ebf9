# 提示词 02: 实现注意力机制

## 任务描述
在Qilin推理引擎中实现完整的注意力机制，包括缩放点积注意力、多头注意力和各种注意力变体。

## 背景信息
当前项目已经定义了注意力机制的基础接口和配置结构。需要实现具体的注意力算法，这是Transformer架构的核心组件。

## 具体要求

### 1. 缩放点积注意力 (Scaled Dot-Product Attention)
在 `src/attention/scaled_dot_product.rs` 中实现：
- **基础算法**: Attention(Q,K,V) = softmax(QK^T/√d_k)V
- **掩码支持**: 因果掩码、填充掩码、自定义掩码
- **数值稳定性**: 防止softmax溢出的优化
- **内存优化**: 支持flash attention风格的内存高效计算

### 2. 多头注意力 (Multi-Head Attention)
在 `src/attention/multi_head.rs` 中实现：
- **并行头计算**: 多个注意力头的并行处理
- **线性投影**: Q、K、V的线性变换
- **输出投影**: 多头结果的合并和投影
- **残差连接**: 支持残差连接和层归一化

### 3. 注意力变体
在 `src/attention/variants/` 目录下实现：
- **自注意力**: Self-Attention机制
- **交叉注意力**: Cross-Attention机制
- **因果注意力**: 用于自回归生成的掩码注意力
- **相对位置编码**: 支持相对位置信息的注意力

### 4. KV缓存机制
在 `src/attention/cache.rs` 中实现：
- **缓存结构**: 高效的键值对缓存
- **增量计算**: 支持自回归生成的增量注意力
- **内存管理**: 动态缓存大小和清理机制
- **批处理支持**: 多序列的缓存管理

## 技术要求

### 性能优化
- **SIMD优化**: 矩阵乘法和softmax的向量化
- **内存布局**: 缓存友好的数据排列
- **并行计算**: 多头和批次的并行处理
- **Flash Attention**: 实现内存高效的注意力算法

### 数值稳定性
- **Softmax稳定性**: 防止指数溢出的数值技巧
- **梯度稳定性**: 确保反向传播的数值稳定
- **精度控制**: 支持不同精度的计算

### 灵活性设计
- **可配置参数**: 头数、维度、dropout等
- **动态形状**: 支持变长序列
- **设备无关**: 统一的CPU/GPU接口

## 代码结构建议

```rust
// src/attention/scaled_dot_product.rs
pub struct ScaledDotProductAttention {
    scale: f32,
    dropout: Option<f32>,
}

impl ScaledDotProductAttention {
    pub fn forward<T: Numeric>(
        &self,
        query: &Tensor<T>,
        key: &Tensor<T>,
        value: &Tensor<T>,
        mask: Option<&Tensor<bool>>,
    ) -> Result<Tensor<T>, AttentionError>;
}

// src/attention/multi_head.rs
pub struct MultiHeadAttention {
    num_heads: usize,
    head_dim: usize,
    q_proj: Linear,
    k_proj: Linear,
    v_proj: Linear,
    out_proj: Linear,
    attention: ScaledDotProductAttention,
}

// src/attention/cache.rs
pub struct KVCache<T: Numeric> {
    keys: Vec<Tensor<T>>,
    values: Vec<Tensor<T>>,
    max_length: usize,
    current_length: usize,
}

impl<T: Numeric> KVCache<T> {
    pub fn update(&mut self, keys: &Tensor<T>, values: &Tensor<T>) -> Result<(), CacheError>;
    pub fn get_cached(&self) -> Result<(Tensor<T>, Tensor<T>), CacheError>;
}
```

## 实现细节

### 1. 注意力计算流程
```rust
// 1. 线性投影
let q = self.q_proj.forward(input)?;
let k = self.k_proj.forward(input)?;
let v = self.v_proj.forward(input)?;

// 2. 重塑为多头
let q = q.reshape_for_heads(self.num_heads)?;
let k = k.reshape_for_heads(self.num_heads)?;
let v = v.reshape_for_heads(self.num_heads)?;

// 3. 缩放点积注意力
let attention_output = self.attention.forward(&q, &k, &v, mask)?;

// 4. 合并多头并投影
let output = attention_output.merge_heads()?;
self.out_proj.forward(&output)
```

### 2. 掩码处理
- **因果掩码**: 下三角矩阵，防止看到未来信息
- **填充掩码**: 忽略填充位置的注意力
- **自定义掩码**: 用户定义的注意力模式

### 3. 内存优化策略
- **就地操作**: 减少临时张量分配
- **分块计算**: 处理超长序列的分块注意力
- **梯度检查点**: 权衡内存和计算的策略

## 测试要求

### 单元测试
- 注意力权重的正确性验证
- 不同掩码类型的测试
- 数值稳定性测试
- 边界条件测试

### 集成测试
- 与Transformer层的集成
- 批处理和序列长度变化
- KV缓存的正确性验证

### 性能测试
- 不同序列长度的性能基准
- 内存使用量测试
- 与参考实现的性能对比

## 验收标准
1. 通过所有单元测试和集成测试
2. 注意力权重计算正确，与参考实现一致
3. 支持各种掩码类型和注意力变体
4. KV缓存机制工作正常，支持增量生成
5. 性能满足预期，内存使用合理
6. 代码文档完整，包含使用示例

## 优先级
1. **最高优先级**: 缩放点积注意力、多头注意力
2. **高优先级**: KV缓存机制、掩码支持
3. **中优先级**: 注意力变体、性能优化
4. **低优先级**: Flash Attention、高级优化

请确保实现的注意力机制与标准Transformer架构兼容，并为后续的模型实现提供坚实基础。
