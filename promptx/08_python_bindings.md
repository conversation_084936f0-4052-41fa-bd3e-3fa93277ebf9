# 提示词 08: 实现Python绑定

## 任务描述
为Qilin推理引擎创建Python绑定，使Python用户能够方便地使用Rust实现的高性能推理引擎。

## 背景信息
当前项目已经实现了完整的Rust推理引擎。需要创建Python绑定来扩大用户群体，让Python开发者能够享受Rust的性能优势。

## 具体要求

### 1. PyO3绑定实现
在 `bindings/python/` 目录下实现：
- **核心绑定**: 张量、模型、推理引擎的Python接口
- **类型转换**: Rust和Python类型的自动转换
- **错误处理**: Python异常的映射和处理
- **内存管理**: 安全的内存管理和生命周期

### 2. Python API设计
在 `bindings/python/comfyui_inference/` 目录下实现：
- **高级API**: 简单易用的Python接口
- **NumPy集成**: 与NumPy数组的无缝转换
- **PyTorch兼容**: 与PyTorch张量的互操作
- **异步支持**: Python异步编程支持

### 3. 包装和分发
在 `bindings/python/` 目录下配置：
- **setuptools配置**: Python包的构建和安装
- **wheel构建**: 跨平台wheel包的构建
- **CI/CD集成**: 自动化的包构建和发布
- **文档生成**: 自动生成Python API文档

### 4. 示例和教程
在 `bindings/python/examples/` 目录下提供：
- **基础使用**: 简单的推理示例
- **高级功能**: 批处理、流式生成等
- **集成示例**: 与其他Python库的集成
- **性能对比**: 与其他推理框架的对比

## 技术要求

### PyO3集成
- **自动类型转换**: 实现Rust和Python类型的自动转换
- **错误传播**: 将Rust错误转换为Python异常
- **GIL管理**: 正确处理Python GIL的获取和释放
- **内存安全**: 确保跨语言边界的内存安全

### 性能优化
- **零拷贝**: 尽可能避免数据拷贝
- **并行处理**: 支持Python多线程和多进程
- **异步IO**: 支持Python异步编程模式
- **批处理优化**: 高效的批量操作

### 易用性设计
- **Pythonic API**: 符合Python习惯的接口设计
- **类型提示**: 完整的类型注解
- **文档字符串**: 详细的API文档
- **错误信息**: 清晰的错误提示

## 代码结构建议

```rust
// bindings/python/src/lib.rs
use pyo3::prelude::*;

mod tensor;
mod model;
mod inference;
mod error;

use tensor::PyTensor;
use model::PyModel;
use inference::PyInferenceEngine;

#[pymodule]
fn qilin_inference(_py: Python, m: &PyModule) -> PyResult<()> {
    m.add_class::<PyTensor>()?;
    m.add_class::<PyModel>()?;
    m.add_class::<PyInferenceEngine>()?;
    
    m.add_function(wrap_pyfunction!(load_model, m)?)?;
    m.add_function(wrap_pyfunction!(create_tensor, m)?)?;
    
    Ok(())
}

// bindings/python/src/tensor.rs
#[pyclass(name = "Tensor")]
pub struct PyTensor {
    inner: Box<dyn Tensor<f32>>,
}

#[pymethods]
impl PyTensor {
    #[new]
    fn new(data: Vec<f32>, shape: Vec<usize>) -> PyResult<Self> {
        let shape = Shape::new(shape);
        let tensor = CpuTensor::from_vec(data, &shape)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(e.to_string()))?;
        
        Ok(PyTensor {
            inner: Box::new(tensor),
        })
    }
    
    fn shape(&self) -> Vec<usize> {
        self.inner.shape().dims().to_vec()
    }
    
    fn to_numpy(&self, py: Python) -> PyResult<PyObject> {
        let data = self.inner.to_vec()
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(e.to_string()))?;
        
        let numpy = py.import("numpy")?;
        let array = numpy.call_method1("array", (data,))?;
        let reshaped = array.call_method1("reshape", (self.shape(),))?;
        
        Ok(reshaped.to_object(py))
    }
    
    fn __add__(&self, other: &PyTensor) -> PyResult<PyTensor> {
        let result = self.inner.add(&*other.inner)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(e.to_string()))?;
        
        Ok(PyTensor {
            inner: Box::new(result),
        })
    }
}
```

## Python API设计

### 1. 高级API
```python
# bindings/python/qilin_inference/__init__.py
from .core import Tensor, Model, InferenceEngine
from .utils import load_model, create_tensor_from_numpy

class QilinInference:
    """High-level interface for Qilin inference engine."""
    
    def __init__(self, model_path: str, device: str = "auto"):
        """Initialize the inference engine.
        
        Args:
            model_path: Path to the model files
            device: Device to use ("cpu", "cuda", "metal", or "auto")
        """
        self.engine = InferenceEngine.from_pretrained(model_path, device=device)
    
    def generate(
        self,
        prompt: str,
        max_tokens: int = 100,
        temperature: float = 1.0,
        top_p: float = 1.0,
        stream: bool = False,
    ) -> Union[str, Iterator[str]]:
        """Generate text from a prompt.
        
        Args:
            prompt: Input text prompt
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            top_p: Top-p sampling parameter
            stream: Whether to return a streaming iterator
            
        Returns:
            Generated text or streaming iterator
        """
        config = GenerationConfig(
            max_new_tokens=max_tokens,
            temperature=temperature,
            top_p=top_p,
        )
        
        if stream:
            return self.engine.generate_stream(prompt, config)
        else:
            return self.engine.generate(prompt, config).text

    def generate_batch(
        self,
        prompts: List[str],
        max_tokens: int = 100,
        **kwargs
    ) -> List[str]:
        """Generate text for multiple prompts in batch."""
        config = GenerationConfig(max_new_tokens=max_tokens, **kwargs)
        results = self.engine.generate_batch(prompts, config)
        return [result.text for result in results]
```

### 2. NumPy集成
```python
# bindings/python/comfyui_inference/numpy_utils.py
import numpy as np
from .core import Tensor

def tensor_from_numpy(array: np.ndarray) -> Tensor:
    """Create a tensor from a NumPy array."""
    if not array.flags.c_contiguous:
        array = np.ascontiguousarray(array)
    
    return Tensor.from_buffer(
        array.tobytes(),
        list(array.shape),
        array.dtype.name
    )

def tensor_to_numpy(tensor: Tensor) -> np.ndarray:
    """Convert a tensor to a NumPy array."""
    data = tensor.to_bytes()
    dtype = tensor.dtype()
    shape = tensor.shape()
    
    array = np.frombuffer(data, dtype=dtype)
    return array.reshape(shape)

# 自动转换装饰器
def auto_convert_numpy(func):
    """Decorator to automatically convert NumPy arrays to tensors."""
    def wrapper(*args, **kwargs):
        converted_args = []
        for arg in args:
            if isinstance(arg, np.ndarray):
                converted_args.append(tensor_from_numpy(arg))
            else:
                converted_args.append(arg)
        
        converted_kwargs = {}
        for key, value in kwargs.items():
            if isinstance(value, np.ndarray):
                converted_kwargs[key] = tensor_from_numpy(value)
            else:
                converted_kwargs[key] = value
        
        result = func(*converted_args, **converted_kwargs)
        
        if isinstance(result, Tensor):
            return tensor_to_numpy(result)
        return result
    
    return wrapper
```

### 3. 异步支持
```python
# bindings/python/comfyui_inference/async_engine.py
import asyncio
from typing import AsyncIterator
from .core import InferenceEngine

class AsyncInferenceEngine:
    """Async wrapper for the inference engine."""
    
    def __init__(self, engine: InferenceEngine):
        self.engine = engine
        self.executor = ThreadPoolExecutor(max_workers=1)
    
    async def generate(
        self,
        prompt: str,
        config: GenerationConfig,
    ) -> str:
        """Async text generation."""
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            self.executor,
            self.engine.generate,
            prompt,
            config
        )
        return result.text
    
    async def generate_stream(
        self,
        prompt: str,
        config: GenerationConfig,
    ) -> AsyncIterator[str]:
        """Async streaming text generation."""
        # 实现异步流式生成
        queue = asyncio.Queue()
        
        def stream_worker():
            for token in self.engine.generate_stream(prompt, config):
                asyncio.run_coroutine_threadsafe(
                    queue.put(token),
                    asyncio.get_event_loop()
                )
            asyncio.run_coroutine_threadsafe(
                queue.put(None),
                asyncio.get_event_loop()
            )
        
        thread = threading.Thread(target=stream_worker)
        thread.start()
        
        while True:
            token = await queue.get()
            if token is None:
                break
            yield token
        
        thread.join()
```

## 构建和分发

### 1. setup.py配置
```python
# bindings/python/setup.py
from setuptools import setup
from pyo3_setuptools_rust import Pyo3RustExtension, build_rust

setup(
    name="comfyui-inference",
    version="0.1.0",
    description="High-performance inference engine for Transformer models",
    author="ComfyUI Team",
    author_email="<EMAIL>",
    url="https://github.com/comfyui/inference-engine",
    packages=["comfyui_inference"],
    rust_extensions=[
        Pyo3RustExtension(
            "comfyui_inference.core",
            path="Cargo.toml",
            binding=Pyo3RustExtension.Pyo3,
            debug=False,
        )
    ],
    install_requires=[
        "numpy>=1.20.0",
        "typing-extensions>=4.0.0",
    ],
    extras_require={
        "torch": ["torch>=1.12.0"],
        "dev": ["pytest>=6.0", "black", "mypy"],
    },
    python_requires=">=3.8",
    zip_safe=False,
    cmdclass={"build_rust": build_rust},
)
```

### 2. CI/CD配置
```yaml
# .github/workflows/python-wheels.yml
name: Build Python Wheels

on:
  push:
    tags: ["v*"]
  pull_request:

jobs:
  build-wheels:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ["3.8", "3.9", "3.10", "3.11"]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install Rust
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        override: true
    
    - name: Build wheel
      run: |
        cd bindings/python
        pip install wheel setuptools-rust
        python setup.py bdist_wheel
    
    - name: Upload wheels
      uses: actions/upload-artifact@v3
      with:
        name: wheels
        path: bindings/python/dist/*.whl
```

## 测试要求

### 单元测试
- Python绑定的功能正确性
- 类型转换的准确性
- 错误处理的完整性
- 内存管理的安全性

### 集成测试
- 与NumPy的集成测试
- 与PyTorch的互操作测试
- 异步功能的测试
- 多线程安全性测试

### 性能测试
- Python vs Rust性能对比
- 内存使用量测试
- 批处理性能验证
- 跨语言调用开销测试

## 验收标准
1. Python API功能完整，易于使用
2. 与NumPy和PyTorch无缝集成
3. 性能损失最小（<5%开销）
4. 内存安全，无内存泄漏
5. 支持主流Python版本和平台
6. 文档完整，包含详细示例

## 优先级
1. **最高优先级**: 核心PyO3绑定、基础API
2. **高优先级**: NumPy集成、错误处理
3. **中优先级**: 异步支持、PyTorch集成
4. **低优先级**: 高级功能、性能优化

请确保Python绑定能够为Python用户提供简单易用的接口，同时保持Rust引擎的高性能特性。
