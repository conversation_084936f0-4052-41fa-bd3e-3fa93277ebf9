# Tensor Operations Completion - Phase 2

## 任务概述

完成 Qilin 推理引擎张量操作系统的剩余实现，包括占位符函数的完整实现、就地操作支持和性能优化。这是在 Phase 1 基础上的进一步完善。

## 前置条件

- Phase 1 已完成：基本数学函数、归约操作、高级操作的核心实现
- 所有现有测试通过
- 基础架构已建立：TensorOps、ReductionOps、AdvancedOps traits

## 任务详细要求

### 1. 完成占位符实现 (高优先级)

#### 1.1 维度特定的极值操作
在 `src/tensor/reduction.rs` 中完成以下函数：

```rust
// 需要完成的函数签名
fn max_dim(&self, dim: usize, keepdim: bool) -> Result<(Self, Self), Self::Error>;
fn min_dim(&self, dim: usize, keepdim: bool) -> Result<(Self, Self), Self::Error>;
```

**实现要求：**
- 沿指定维度计算最大值/最小值
- 返回值和索引的元组 `(values, indices)`
- 支持 `keepdim` 参数保持维度
- 正确处理多维张量的维度折叠
- 索引张量应为整数类型 (i64)

**测试要求：**
- 测试 1D、2D、3D 张量
- 验证 keepdim=true/false 的行为
- 检查返回的索引正确性

#### 1.2 参数极值操作
```rust
fn argmax(&self, dim: usize, keepdim: bool) -> Result<Self, Self::Error>;
fn argmin(&self, dim: usize, keepdim: bool) -> Result<Self, Self::Error>;
```

**实现要求：**
- 返回最大值/最小值的索引
- 索引类型为 i64
- 支持多维张量和维度指定
- 处理相同值的情况（返回第一个出现的索引）

#### 1.3 逻辑归约操作
```rust
fn all(&self, dim: Option<usize>, keepdim: bool) -> Result<Self, Self::Error>;
fn any(&self, dim: Option<usize>, keepdim: bool) -> Result<Self, Self::Error>;
```

**实现要求：**
- `all`: 所有元素非零时返回 true
- `any`: 任意元素非零时返回 true
- 支持全局操作 (dim=None) 和维度特定操作
- 返回布尔张量 (用 f32 的 0.0/1.0 表示)

### 2. 就地操作支持 (高优先级)

#### 2.1 就地操作 Trait
创建新文件 `src/tensor/inplace.rs`，定义就地操作特征：

```rust
pub trait InplaceOps<T: Numeric> {
    // 数学函数就地版本
    fn sin_inplace(&mut self) -> Result<(), Self::Error>;
    fn cos_inplace(&mut self) -> Result<(), Self::Error>;
    fn exp_inplace(&mut self) -> Result<(), Self::Error>;
    fn log_inplace(&mut self) -> Result<(), Self::Error>;
    fn sqrt_inplace(&mut self) -> Result<(), Self::Error>;
    fn abs_inplace(&mut self) -> Result<(), Self::Error>;
    fn floor_inplace(&mut self) -> Result<(), Self::Error>;
    fn ceil_inplace(&mut self) -> Result<(), Self::Error>;
    fn round_inplace(&mut self) -> Result<(), Self::Error>;
    
    // 二元操作就地版本
    fn add_inplace(&mut self, other: &Self) -> Result<(), Self::Error>;
    fn sub_inplace(&mut self, other: &Self) -> Result<(), Self::Error>;
    fn mul_inplace(&mut self, other: &Self) -> Result<(), Self::Error>;
    fn div_inplace(&mut self, other: &Self) -> Result<(), Self::Error>;
    
    // 标量操作就地版本
    fn add_scalar_inplace(&mut self, scalar: T) -> Result<(), Self::Error>;
    fn mul_scalar_inplace(&mut self, scalar: T) -> Result<(), Self::Error>;
    fn clamp_inplace(&mut self, min: T, max: T) -> Result<(), Self::Error>;
    
    // 掩码操作就地版本
    fn masked_fill_inplace(&mut self, mask: &Self, value: T) -> Result<(), Self::Error>;
}
```

#### 2.2 内存池优化
创建 `src/tensor/memory_pool.rs`：

```rust
pub struct TensorMemoryPool<T: Numeric> {
    // 内存池实现
}

impl<T: Numeric> TensorMemoryPool<T> {
    pub fn new(initial_capacity: usize) -> Self;
    pub fn allocate(&mut self, size: usize) -> Vec<T>;
    pub fn deallocate(&mut self, buffer: Vec<T>);
    pub fn clear(&mut self);
}
```

#### 2.3 视图操作支持
扩展 `src/tensor/mod.rs`，添加视图操作：

```rust
pub trait TensorView<T: Numeric> {
    fn view(&self, shape: &Shape) -> Result<Self, Self::Error>;
    fn view_as(&self, other: &Self) -> Result<Self, Self::Error>;
    fn transpose_view(&self, dim0: usize, dim1: usize) -> Result<Self, Self::Error>;
    fn slice_view(&self, ranges: &[Range<usize>]) -> Result<Self, Self::Error>;
}
```

### 3. 高级操作完善 (中优先级)

#### 3.1 张量分割操作
在 `src/tensor/advanced.rs` 中完成：

```rust
fn split(&self, split_size: usize, dim: usize) -> Result<Vec<Self>, Self::Error>;
fn chunk(&self, chunks: usize, dim: usize) -> Result<Vec<Self>, Self::Error>;
```

**实现要求：**
- `split`: 按固定大小分割张量
- `chunk`: 分割成指定数量的块
- 处理不能整除的情况
- 返回张量向量

#### 3.2 索引操作
```rust
fn gather(&self, dim: usize, index: &Self) -> Result<Self, Self::Error>;
fn scatter(&self, dim: usize, index: &Self, src: &Self) -> Result<Self, Self::Error>;
```

**实现要求：**
- `gather`: 根据索引收集元素
- `scatter`: 根据索引分散元素
- 支持多维索引
- 边界检查和错误处理

#### 3.3 排序操作
```rust
fn sort(&self, dim: usize, descending: bool) -> Result<(Self, Self), Self::Error>;
fn argsort(&self, dim: usize, descending: bool) -> Result<Self, Self::Error>;
fn topk(&self, k: usize, dim: usize, largest: bool) -> Result<(Self, Self), Self::Error>;
```

**实现要求：**
- 稳定排序算法
- 返回排序后的值和索引
- `topk` 返回前 k 个最大/最小值

#### 3.4 掩码选择
```rust
fn masked_select(&self, mask: &Self) -> Result<Self, Self::Error>;
```

**实现要求：**
- 根据掩码选择元素
- 返回一维张量
- 处理不同形状的掩码

### 4. 性能优化 (中优先级)

#### 4.1 SIMD 优化
创建 `src/tensor/simd.rs`：

```rust
#[cfg(target_arch = "x86_64")]
mod x86_64_simd {
    use std::arch::x86_64::*;
    
    pub unsafe fn simd_add_f32(a: &[f32], b: &[f32], result: &mut [f32]);
    pub unsafe fn simd_mul_f32(a: &[f32], b: &[f32], result: &mut [f32]);
    pub unsafe fn simd_exp_f32(input: &[f32], result: &mut [f32]);
    // 更多 SIMD 函数
}
```

#### 4.2 并行计算支持
使用 `rayon` crate 添加并行支持：

```rust
// 在 Cargo.toml 中添加
[dependencies]
rayon = "1.7"

// 在操作中使用并行迭代器
use rayon::prelude::*;

fn parallel_element_wise_op<F>(&self, other: &Self, op: F) -> Result<Self, Self::Error>
where
    F: Fn(T, T) -> T + Sync + Send,
{
    // 并行实现
}
```

### 5. 测试和文档 (低优先级)

#### 5.1 性能基准测试
创建 `benches/tensor_ops_bench.rs`：

```rust
use criterion::{black_box, criterion_group, criterion_main, Criterion};

fn benchmark_math_ops(c: &mut Criterion) {
    // 数学函数性能测试
}

fn benchmark_reduction_ops(c: &mut Criterion) {
    // 归约操作性能测试
}

criterion_group!(benches, benchmark_math_ops, benchmark_reduction_ops);
criterion_main!(benches);
```

#### 5.2 API 文档完善
为所有新函数添加详细的文档注释：

```rust
/// 计算张量沿指定维度的最大值和对应索引
/// 
/// # 参数
/// * `dim` - 计算最大值的维度
/// * `keepdim` - 是否保持原始维度
/// 
/// # 返回值
/// 返回 `(values, indices)` 元组，其中：
/// * `values` - 最大值张量
/// * `indices` - 对应索引张量
/// 
/// # 示例
/// ```rust
/// let tensor = from_slice(&[1.0, 3.0, 2.0, 4.0], &Shape::new(vec![2, 2]))?;
/// let (max_vals, indices) = tensor.max_dim(1, false)?;
/// ```
fn max_dim(&self, dim: usize, keepdim: bool) -> Result<(Self, Self), Self::Error>;
```

## 实现顺序建议

1. **第一阶段**：完成占位符实现
   - max_dim, min_dim
   - argmax, argmin
   - all, any

2. **第二阶段**：就地操作支持
   - InplaceOps trait
   - 基本就地操作实现
   - 内存池基础设施

3. **第三阶段**：高级操作完善
   - split, chunk
   - gather, scatter
   - sort, argsort, topk

4. **第四阶段**：性能优化
   - SIMD 优化
   - 并行计算
   - 内存访问优化

## 验收标准

- [ ] 所有占位符函数完整实现
- [ ] 就地操作 trait 定义和基本实现
- [ ] 高级操作完整实现
- [ ] 所有新功能有对应的单元测试
- [ ] 性能基准测试建立
- [ ] 文档完善，包含使用示例
- [ ] 所有测试通过，无回归
- [ ] 内存安全验证

## 注意事项

1. **内存安全**：确保所有就地操作不会导致数据竞争
2. **数值稳定性**：排序和索引操作要处理 NaN 和无穷大
3. **错误处理**：提供清晰的错误信息和上下文
4. **性能**：避免不必要的内存分配和复制
5. **兼容性**：保持与现有 API 的兼容性
