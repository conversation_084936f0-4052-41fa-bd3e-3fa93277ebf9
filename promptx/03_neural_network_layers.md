# 提示词 03: 实现神经网络层

## 任务描述
在Qilin推理引擎中实现完整的神经网络层系统，包括线性层、归一化层、激活函数层等Transformer架构所需的核心组件。

## 背景信息
当前项目已经定义了层的基础接口和配置结构。需要实现具体的神经网络层，为构建完整的Transformer模型提供基础组件。

## 具体要求

### 1. 线性层 (Linear/Dense Layer)
在 `src/layers/linear.rs` 中实现：
- **基础线性变换**: y = xW^T + b
- **权重初始化**: Xavier、He、正态分布等初始化策略
- **偏置支持**: 可选的偏置项
- **批处理支持**: 高效的批量线性变换

### 2. 归一化层
在 `src/layers/normalization/` 目录下实现：
- **LayerNorm**: 层归一化，支持可学习的缩放和偏移
- **RMSNorm**: RMS归一化，更高效的归一化变体
- **BatchNorm**: 批归一化（用于训练时的兼容性）
- **GroupNorm**: 组归一化

### 3. 激活函数层
在 `src/layers/activation.rs` 中实现：
- **基础激活**: ReLU, GELU, Swish, Mish
- **门控激活**: GLU, SwiGLU, GeGLU
- **可配置激活**: 支持不同的激活函数参数

### 4. 嵌入层
在 `src/layers/embedding.rs` 中实现：
- **词嵌入**: Token embedding with vocabulary lookup
- **位置嵌入**: 绝对位置编码和相对位置编码
- **旋转位置嵌入**: RoPE (Rotary Position Embedding)
- **嵌入缩放**: 支持嵌入维度的缩放

### 5. 前馈网络层
在 `src/layers/feedforward.rs` 中实现：
- **标准FFN**: Linear -> Activation -> Linear
- **门控FFN**: 支持GLU系列的门控机制
- **专家混合**: MoE (Mixture of Experts) 基础结构

## 技术要求

### 性能优化
- **SIMD优化**: 矩阵乘法和向量运算的优化
- **内存布局**: 权重矩阵的最优存储格式
- **批处理**: 高效的批量计算
- **缓存友好**: 优化内存访问模式

### 参数管理
- **权重共享**: 支持参数共享机制
- **参数初始化**: 多种初始化策略
- **参数冻结**: 支持部分参数的冻结
- **参数统计**: 参数数量和内存使用统计

### 数值稳定性
- **归一化稳定性**: 防止除零和数值溢出
- **激活函数稳定性**: 处理极值输入
- **梯度稳定性**: 确保数值计算的稳定性

## 代码结构建议

```rust
// src/layers/linear.rs
pub struct Linear<T: Numeric> {
    weight: Tensor<T>,
    bias: Option<Tensor<T>>,
    in_features: usize,
    out_features: usize,
}

impl<T: Numeric> Linear<T> {
    pub fn new(in_features: usize, out_features: usize, bias: bool) -> Self;
    pub fn from_weights(weight: Tensor<T>, bias: Option<Tensor<T>>) -> Self;
}

impl<T: Numeric> Layer<T> for Linear<T> {
    fn forward(&self, input: &Tensor<T>) -> Result<Tensor<T>, LayerError>;
    fn parameters(&self) -> Vec<&Tensor<T>>;
}

// src/layers/normalization/layer_norm.rs
pub struct LayerNorm<T: Numeric> {
    normalized_shape: Vec<usize>,
    weight: Tensor<T>,
    bias: Tensor<T>,
    eps: T,
}

// src/layers/embedding.rs
pub struct Embedding<T: Numeric> {
    weight: Tensor<T>,
    vocab_size: usize,
    embedding_dim: usize,
    padding_idx: Option<usize>,
}

pub struct PositionalEncoding<T: Numeric> {
    encoding_type: PositionEncodingType,
    max_length: usize,
    embedding_dim: usize,
}

// src/layers/feedforward.rs
pub struct FeedForward<T: Numeric> {
    linear1: Linear<T>,
    linear2: Linear<T>,
    activation: Box<dyn ActivationFunction<T>>,
    dropout: Option<f32>,
}
```

## 实现细节

### 1. 线性层实现
```rust
impl<T: Numeric> Layer<T> for Linear<T> {
    fn forward(&self, input: &Tensor<T>) -> Result<Tensor<T>, LayerError> {
        // 矩阵乘法: input @ weight.T
        let output = input.matmul(&self.weight.transpose(-2, -1)?)?;
        
        // 添加偏置
        if let Some(bias) = &self.bias {
            output.add(bias)
        } else {
            Ok(output)
        }
    }
}
```

### 2. LayerNorm实现
```rust
impl<T: Numeric> Layer<T> for LayerNorm<T> {
    fn forward(&self, input: &Tensor<T>) -> Result<Tensor<T>, LayerError> {
        // 计算均值和方差
        let mean = input.mean(Some(-1), true)?;
        let var = input.var(Some(-1), true, false)?;
        
        // 归一化
        let normalized = (input - &mean)? / &(var + self.eps)?.sqrt()?;
        
        // 缩放和偏移
        (normalized * &self.weight)? + &self.bias
    }
}
```

### 3. 位置编码实现
```rust
impl<T: Numeric> PositionalEncoding<T> {
    fn sinusoidal_encoding(&self, seq_len: usize) -> Result<Tensor<T>, EmbeddingError> {
        // 实现正弦位置编码
        // PE(pos, 2i) = sin(pos / 10000^(2i/d_model))
        // PE(pos, 2i+1) = cos(pos / 10000^(2i/d_model))
    }
    
    fn rotary_encoding(&self, seq_len: usize) -> Result<Tensor<T>, EmbeddingError> {
        // 实现旋转位置编码 (RoPE)
    }
}
```

## 层组合和配置

### 1. 层工厂模式
```rust
pub struct LayerFactory;

impl LayerFactory {
    pub fn create_linear<T: Numeric>(config: &LinearConfig) -> Result<Linear<T>, LayerError>;
    pub fn create_layer_norm<T: Numeric>(config: &NormConfig) -> Result<LayerNorm<T>, LayerError>;
    pub fn create_embedding<T: Numeric>(config: &EmbedConfig) -> Result<Embedding<T>, LayerError>;
}
```

### 2. 层序列
```rust
pub struct Sequential<T: Numeric> {
    layers: Vec<Box<dyn Layer<T>>>,
}

impl<T: Numeric> Sequential<T> {
    pub fn add_layer(&mut self, layer: Box<dyn Layer<T>>);
    pub fn forward(&self, input: &Tensor<T>) -> Result<Tensor<T>, LayerError>;
}
```

## 测试要求

### 单元测试
- 每个层的前向传播正确性
- 参数初始化的合理性
- 边界条件和错误处理
- 数值稳定性验证

### 集成测试
- 层之间的组合使用
- 不同批大小和序列长度
- 参数共享和冻结功能

### 性能测试
- 各层的计算性能基准
- 内存使用量测试
- 批处理效率验证

## 验收标准
1. 所有层都通过单元测试和集成测试
2. 前向传播结果与参考实现一致
3. 支持不同的参数初始化策略
4. 内存使用合理，性能满足要求
5. 代码结构清晰，易于扩展
6. 文档完整，包含使用示例

## 优先级
1. **最高优先级**: 线性层、LayerNorm、基础激活函数
2. **高优先级**: 嵌入层、位置编码、前馈网络
3. **中优先级**: 其他归一化层、门控激活函数
4. **低优先级**: MoE、高级优化特性

请确保实现的神经网络层与现有的张量系统和注意力机制无缝集成，为构建完整的Transformer模型做好准备。
