# 提示词 05: 实现模型加载和权重管理

## 任务描述
在Qilin推理引擎中实现完整的模型加载系统，支持从文件加载预训练权重、模型序列化/反序列化、权重格式转换等功能。

## 背景信息
当前项目已经实现了Transformer的核心组件。需要建立模型加载和权重管理系统，使引擎能够加载和使用预训练的模型权重。

## 具体要求

### 1. 权重加载系统
在 `src/model/loader.rs` 中实现：
- **SafeTensors支持**: 加载SafeTensors格式的权重文件
- **PyTorch权重**: 支持.pth和.bin格式的权重文件
- **GGUF支持**: 支持量化模型的GGUF格式
- **HuggingFace兼容**: 支持HuggingFace模型仓库的权重格式

### 2. 模型注册和工厂
在 `src/model/registry.rs` 中实现：
- **模型注册**: 注册不同的模型架构
- **模型工厂**: 根据配置创建模型实例
- **架构检测**: 自动检测模型架构类型
- **版本兼容**: 处理不同版本的模型格式

### 3. 权重映射和转换
在 `src/model/mapping.rs` 中实现：
- **权重名称映射**: 不同框架间的权重名称转换
- **形状转换**: 处理权重形状的差异
- **数据类型转换**: 支持不同精度的权重转换
- **分片权重**: 处理大模型的分片权重文件

### 4. 模型序列化
在 `src/model/serialization.rs` 中实现：
- **模型保存**: 将模型权重保存为文件
- **配置保存**: 保存模型配置信息
- **检查点管理**: 支持训练检查点的保存和加载
- **压缩支持**: 权重文件的压缩和解压

## 技术要求

### 内存管理
- **延迟加载**: 按需加载权重，减少内存占用
- **内存映射**: 使用mmap减少内存拷贝
- **权重共享**: 支持多个模型实例共享权重
- **垃圾回收**: 及时释放不需要的权重

### 错误处理
- **文件验证**: 验证权重文件的完整性
- **版本检查**: 检查模型版本兼容性
- **权重验证**: 验证权重的形状和类型
- **回退机制**: 加载失败时的回退策略

### 性能优化
- **并行加载**: 多线程并行加载权重
- **缓存机制**: 缓存常用的权重文件
- **预加载**: 预测性地加载可能需要的权重
- **流式加载**: 支持大文件的流式加载

## 代码结构建议

```rust
// src/model/loader.rs
pub struct ModelLoader {
    cache_dir: PathBuf,
    device: Device,
    dtype: DType,
}

impl ModelLoader {
    pub fn load_model<T: Numeric>(
        &self,
        model_path: &Path,
        config: Option<&ModelConfig>,
    ) -> Result<Box<dyn Model<T>>, ModelError>;
    
    pub fn load_weights<T: Numeric>(
        &self,
        weights_path: &Path,
    ) -> Result<HashMap<String, Tensor<T>>, ModelError>;
    
    pub fn load_safetensors<T: Numeric>(
        &self,
        file_path: &Path,
    ) -> Result<HashMap<String, Tensor<T>>, ModelError>;
}

// src/model/registry.rs
pub struct ModelRegistry {
    architectures: HashMap<String, Box<dyn ModelFactory>>,
}

pub trait ModelFactory: Send + Sync {
    fn create_model<T: Numeric>(
        &self,
        config: &ModelConfig,
        weights: Option<HashMap<String, Tensor<T>>>,
    ) -> Result<Box<dyn Model<T>>, ModelError>;
    
    fn supported_architectures(&self) -> Vec<&str>;
}

// src/model/mapping.rs
pub struct WeightMapper {
    mappings: HashMap<String, WeightMapping>,
}

#[derive(Debug, Clone)]
pub struct WeightMapping {
    pub source_name: String,
    pub target_name: String,
    pub transform: Option<WeightTransform>,
}

#[derive(Debug, Clone)]
pub enum WeightTransform {
    Transpose(Vec<usize>),
    Reshape(Vec<usize>),
    Split { dim: usize, chunks: usize },
    Concat { dim: usize },
}
```

## 实现细节

### 1. SafeTensors加载
```rust
impl ModelLoader {
    pub fn load_safetensors<T: Numeric>(
        &self,
        file_path: &Path,
    ) -> Result<HashMap<String, Tensor<T>>, ModelError> {
        let file = File::open(file_path)?;
        let mmap = unsafe { MmapOptions::new().map(&file)? };
        
        // 解析SafeTensors头部
        let header_size = u64::from_le_bytes(
            mmap[0..8].try_into().map_err(|_| ModelError::InvalidFormat)?
        ) as usize;
        
        let header_bytes = &mmap[8..8 + header_size];
        let header: SafeTensorsHeader = serde_json::from_slice(header_bytes)?;
        
        let mut tensors = HashMap::new();
        
        for (name, info) in header.tensors {
            let start = 8 + header_size + info.data_offsets[0];
            let end = 8 + header_size + info.data_offsets[1];
            let data = &mmap[start..end];
            
            let tensor = self.create_tensor_from_bytes::<T>(data, &info.shape, &info.dtype)?;
            tensors.insert(name, tensor);
        }
        
        Ok(tensors)
    }
}
```

### 2. 权重映射
```rust
impl WeightMapper {
    pub fn map_weights<T: Numeric>(
        &self,
        source_weights: HashMap<String, Tensor<T>>,
        target_architecture: &str,
    ) -> Result<HashMap<String, Tensor<T>>, ModelError> {
        let mut mapped_weights = HashMap::new();
        
        for (source_name, tensor) in source_weights {
            if let Some(mapping) = self.mappings.get(&source_name) {
                let transformed_tensor = self.apply_transform(&tensor, &mapping.transform)?;
                mapped_weights.insert(mapping.target_name.clone(), transformed_tensor);
            } else {
                // 尝试自动映射
                if let Some(target_name) = self.auto_map_name(&source_name, target_architecture) {
                    mapped_weights.insert(target_name, tensor);
                }
            }
        }
        
        Ok(mapped_weights)
    }
    
    fn apply_transform<T: Numeric>(
        &self,
        tensor: &Tensor<T>,
        transform: &Option<WeightTransform>,
    ) -> Result<Tensor<T>, ModelError> {
        match transform {
            Some(WeightTransform::Transpose(dims)) => tensor.transpose(dims),
            Some(WeightTransform::Reshape(shape)) => tensor.reshape(&Shape::new(shape.clone())),
            Some(WeightTransform::Split { dim, chunks }) => {
                // 实现张量分割
                todo!("Implement tensor split")
            },
            None => Ok(tensor.clone()),
        }
    }
}
```

### 3. 模型工厂
```rust
pub struct GPTModelFactory;

impl ModelFactory for GPTModelFactory {
    fn create_model<T: Numeric>(
        &self,
        config: &ModelConfig,
        weights: Option<HashMap<String, Tensor<T>>>,
    ) -> Result<Box<dyn Model<T>>, ModelError> {
        let mut model = GPTModel::new(config)?;
        
        if let Some(weights) = weights {
            model.load_weights(weights)?;
        }
        
        Ok(Box::new(model))
    }
    
    fn supported_architectures(&self) -> Vec<&str> {
        vec!["gpt", "gpt2", "gpt-neo", "gpt-j"]
    }
}
```

### 4. 配置加载
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelConfig {
    pub architecture: String,
    pub model_type: String,
    pub vocab_size: usize,
    pub hidden_size: usize,
    pub num_layers: usize,
    pub num_attention_heads: usize,
    pub intermediate_size: Option<usize>,
    pub max_position_embeddings: usize,
    pub layer_norm_eps: f32,
    pub dropout: f32,
    pub activation_function: String,
    pub use_cache: bool,
    pub torch_dtype: String,
}

impl ModelConfig {
    pub fn from_json(path: &Path) -> Result<Self, ModelError> {
        let content = std::fs::read_to_string(path)?;
        let config: Self = serde_json::from_str(&content)?;
        Ok(config)
    }
    
    pub fn from_hf_config(hf_config: &HuggingFaceConfig) -> Result<Self, ModelError> {
        // 转换HuggingFace配置格式
        todo!("Convert HuggingFace config")
    }
}
```

## 支持的模型格式

### 1. 权重文件格式
- **SafeTensors**: 推荐的安全权重格式
- **PyTorch**: .pth, .bin格式
- **GGUF**: 量化模型格式
- **NumPy**: .npz格式（用于调试）

### 2. 配置文件格式
- **JSON**: 标准的JSON配置文件
- **YAML**: 人类可读的YAML格式
- **TOML**: 简洁的TOML格式

### 3. 模型架构支持
- **GPT系列**: GPT, GPT-2, GPT-Neo, GPT-J
- **BERT系列**: BERT, RoBERTa, DeBERTa
- **T5系列**: T5, UL2, PaLM
- **LLaMA系列**: LLaMA, Alpaca, Vicuna

## 测试要求

### 单元测试
- 各种权重格式的加载测试
- 权重映射和转换的正确性
- 配置文件解析测试
- 错误处理和边界条件

### 集成测试
- 完整模型的加载和推理
- 不同架构模型的兼容性
- 大模型的内存管理
- 多线程加载的安全性

### 性能测试
- 加载速度基准测试
- 内存使用量测试
- 并发加载性能
- 缓存效果验证

## 验收标准
1. 支持主流的权重文件格式
2. 能够加载常见的预训练模型
3. 权重映射和转换功能正确
4. 内存使用合理，加载速度快
5. 错误处理完善，提供清晰的错误信息
6. 代码文档完整，包含使用示例

## 优先级
1. **最高优先级**: SafeTensors加载、基础权重映射
2. **高优先级**: 模型工厂、配置系统
3. **中优先级**: PyTorch格式支持、HuggingFace兼容
4. **低优先级**: GGUF支持、高级优化特性

请确保实现的模型加载系统能够无缝集成到现有的推理引擎中，并为用户提供简单易用的模型加载接口。
