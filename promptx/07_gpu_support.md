# 提示词 07: 实现GPU支持 (CUDA和Metal)

## 任务描述
在Qilin推理引擎中实现GPU支持，包括CUDA和Metal后端，提供高性能的GPU加速推理能力。

## 背景信息
当前项目已经实现了CPU版本的推理引擎。需要添加GPU支持来大幅提升推理性能，特别是对于大型模型的推理。

## 具体要求

### 1. CUDA后端实现
在 `src/tensor/cuda/` 目录下实现：
- **CUDA张量**: 基于CUDA的张量实现
- **CUDA操作**: GPU上的数学运算和张量操作
- **内存管理**: CUDA内存分配和释放
- **流管理**: CUDA流的创建和同步

### 2. Metal后端实现
在 `src/tensor/metal/` 目录下实现：
- **Metal张量**: 基于Metal的张量实现
- **Metal着色器**: GPU计算着色器
- **缓冲区管理**: Metal缓冲区的管理
- **命令队列**: Metal命令队列和编码器

### 3. 设备抽象统一
在 `src/tensor/device.rs` 中扩展：
- **设备检测**: 自动检测可用的GPU设备
- **设备选择**: 智能的设备选择策略
- **内存查询**: 查询设备内存信息
- **性能特性**: 查询设备计算能力

### 4. 跨设备操作
在 `src/tensor/cross_device.rs` 中实现：
- **数据传输**: CPU-GPU间的数据传输
- **设备同步**: 不同设备间的同步机制
- **混合计算**: CPU和GPU的协同计算
- **负载均衡**: 多GPU间的负载分配

## 技术要求

### CUDA实现要求
- **cuBLAS集成**: 高性能的线性代数运算
- **cuDNN集成**: 深度学习专用的优化算子
- **内存池**: 高效的GPU内存池管理
- **异步执行**: 利用CUDA流的异步计算

### Metal实现要求
- **Metal Performance Shaders**: 使用MPS框架
- **计算着色器**: 自定义的GPU计算内核
- **统一内存**: 利用Apple的统一内存架构
- **神经引擎**: 集成Apple Neural Engine

### 性能优化
- **内核融合**: 合并多个GPU操作
- **内存合并**: 优化内存访问模式
- **占用率优化**: 最大化GPU利用率
- **精度优化**: 支持FP16和INT8计算

## 代码结构建议

```rust
// src/tensor/cuda/mod.rs
pub mod tensor;
pub mod ops;
pub mod memory;
pub mod stream;

use cudarc::driver::{CudaDevice, CudaStream};
use cudarc::cublas::CublasLt;

pub struct CudaBackend {
    device: Arc<CudaDevice>,
    stream: CudaStream,
    cublas: CublasLt,
    memory_pool: CudaMemoryPool,
}

// src/tensor/cuda/tensor.rs
pub struct CudaTensor<T: Numeric> {
    data: CudaSlice<T>,
    shape: Shape,
    device: Arc<CudaDevice>,
    stream: CudaStream,
}

impl<T: Numeric> Tensor<T> for CudaTensor<T> {
    type Error = CudaError;
    
    fn shape(&self) -> &Shape { &self.shape }
    fn device(&self) -> &Device { &Device::Cuda(self.device.clone()) }
    
    fn to_cpu(&self) -> Result<CpuTensor<T>, Self::Error>;
    fn to_cuda(&self) -> Result<CudaTensor<T>, Self::Error>;
}

// src/tensor/metal/mod.rs
pub mod tensor;
pub mod shaders;
pub mod buffers;
pub mod command_queue;

use metal::{Device as MetalDevice, CommandQueue, Buffer};

pub struct MetalBackend {
    device: MetalDevice,
    command_queue: CommandQueue,
    library: metal::Library,
    buffer_pool: MetalBufferPool,
}

// src/tensor/metal/tensor.rs
pub struct MetalTensor<T: Numeric> {
    buffer: Buffer,
    shape: Shape,
    device: MetalDevice,
    command_queue: CommandQueue,
}
```

## 实现细节

### 1. CUDA张量操作
```rust
impl<T: Numeric> TensorOps<T> for CudaTensor<T> {
    fn add(&self, other: &Self) -> Result<Self, Self::Error> {
        let output_data = self.device.alloc_zeros::<T>(self.shape.size())?;
        
        // 使用cuBLAS进行向量加法
        unsafe {
            self.cublas.axpy(
                self.shape.size(),
                &T::one(),
                &self.data,
                1,
                &mut output_data,
                1,
            )?;
            
            self.cublas.axpy(
                self.shape.size(),
                &T::one(),
                &other.data,
                1,
                &mut output_data,
                1,
            )?;
        }
        
        Ok(CudaTensor {
            data: output_data,
            shape: self.shape.clone(),
            device: self.device.clone(),
            stream: self.stream.clone(),
        })
    }
    
    fn matmul(&self, other: &Self) -> Result<Self, Self::Error> {
        // 使用cuBLAS进行矩阵乘法
        let (m, k) = (self.shape.dims()[0], self.shape.dims()[1]);
        let (k2, n) = (other.shape.dims()[0], other.shape.dims()[1]);
        
        assert_eq!(k, k2, "Matrix dimensions must match for multiplication");
        
        let output_shape = Shape::new(vec![m, n]);
        let output_data = self.device.alloc_zeros::<T>(output_shape.size())?;
        
        unsafe {
            self.cublas.gemm(
                cublas::Operation::None,
                cublas::Operation::None,
                n, m, k,
                &T::one(),
                &other.data, n,
                &self.data, k,
                &T::zero(),
                &mut output_data, n,
            )?;
        }
        
        Ok(CudaTensor {
            data: output_data,
            shape: output_shape,
            device: self.device.clone(),
            stream: self.stream.clone(),
        })
    }
}
```

### 2. Metal计算着色器
```metal
// src/tensor/metal/shaders/basic_ops.metal
#include <metal_stdlib>
using namespace metal;

kernel void vector_add(device const float* a [[buffer(0)]],
                      device const float* b [[buffer(1)]],
                      device float* result [[buffer(2)]],
                      uint index [[thread_position_in_grid]]) {
    result[index] = a[index] + b[index];
}

kernel void matrix_multiply(device const float* a [[buffer(0)]],
                           device const float* b [[buffer(1)]],
                           device float* c [[buffer(2)]],
                           constant uint& M [[buffer(3)]],
                           constant uint& N [[buffer(4)]],
                           constant uint& K [[buffer(5)]],
                           uint2 gid [[thread_position_in_grid]]) {
    uint row = gid.y;
    uint col = gid.x;
    
    if (row >= M || col >= N) return;
    
    float sum = 0.0;
    for (uint k = 0; k < K; k++) {
        sum += a[row * K + k] * b[k * N + col];
    }
    c[row * N + col] = sum;
}
```

### 3. Metal张量操作
```rust
impl<T: Numeric> TensorOps<T> for MetalTensor<T> {
    fn add(&self, other: &Self) -> Result<Self, Self::Error> {
        let output_buffer = self.device.new_buffer(
            (self.shape.size() * std::mem::size_of::<T>()) as u64,
            metal::MTLResourceOptions::StorageModeShared,
        );
        
        let command_buffer = self.command_queue.new_command_buffer();
        let encoder = command_buffer.new_compute_command_encoder();
        
        let pipeline_state = self.get_pipeline_state("vector_add")?;
        encoder.set_compute_pipeline_state(&pipeline_state);
        encoder.set_buffer(0, Some(&self.buffer), 0);
        encoder.set_buffer(1, Some(&other.buffer), 0);
        encoder.set_buffer(2, Some(&output_buffer), 0);
        
        let threads_per_threadgroup = metal::MTLSize::new(256, 1, 1);
        let threadgroups = metal::MTLSize::new(
            (self.shape.size() + 255) / 256,
            1,
            1,
        );
        
        encoder.dispatch_threadgroups(threadgroups, threads_per_threadgroup);
        encoder.end_encoding();
        command_buffer.commit();
        command_buffer.wait_until_completed();
        
        Ok(MetalTensor {
            buffer: output_buffer,
            shape: self.shape.clone(),
            device: self.device.clone(),
            command_queue: self.command_queue.clone(),
        })
    }
}
```

### 4. 设备管理
```rust
impl DeviceManager {
    pub fn detect_devices() -> Vec<Device> {
        let mut devices = vec![Device::Cpu(CpuDevice::new())];
        
        // 检测CUDA设备
        #[cfg(feature = "cuda")]
        {
            if let Ok(cuda_devices) = cudarc::driver::CudaDevice::all() {
                for device in cuda_devices {
                    devices.push(Device::Cuda(device));
                }
            }
        }
        
        // 检测Metal设备
        #[cfg(feature = "metal")]
        {
            if let Some(metal_device) = metal::Device::system_default() {
                devices.push(Device::Metal(metal_device));
            }
        }
        
        devices
    }
    
    pub fn select_best_device(devices: &[Device]) -> &Device {
        // 优先选择GPU设备
        for device in devices {
            match device {
                Device::Cuda(_) => return device,
                Device::Metal(_) => return device,
                _ => continue,
            }
        }
        
        // 回退到CPU
        &devices[0]
    }
}
```

### 5. 跨设备数据传输
```rust
impl<T: Numeric> CrossDeviceOps<T> for Tensor<T> {
    fn to_device(&self, target_device: &Device) -> Result<Box<dyn Tensor<T>>, DeviceError> {
        match (self.device(), target_device) {
            (Device::Cpu(_), Device::Cuda(cuda_device)) => {
                let cpu_tensor = self.as_any().downcast_ref::<CpuTensor<T>>().unwrap();
                let cuda_data = cuda_device.htod_copy(cpu_tensor.data())?;
                
                Ok(Box::new(CudaTensor {
                    data: cuda_data,
                    shape: self.shape().clone(),
                    device: cuda_device.clone(),
                    stream: cuda_device.default_stream(),
                }))
            },
            (Device::Cuda(cuda_device), Device::Cpu(_)) => {
                let cuda_tensor = self.as_any().downcast_ref::<CudaTensor<T>>().unwrap();
                let cpu_data = cuda_device.dtoh_sync_copy(&cuda_tensor.data)?;
                
                Ok(Box::new(CpuTensor::from_vec(cpu_data, self.shape())))
            },
            // 其他设备转换...
            _ => Err(DeviceError::UnsupportedTransfer),
        }
    }
}
```

## 性能优化策略

### 1. 内存管理优化
- **内存池**: 预分配GPU内存池，减少分配开销
- **内存对齐**: 确保内存访问对齐，提升带宽利用率
- **统一内存**: 在支持的平台上使用统一内存

### 2. 计算优化
- **内核融合**: 将多个操作融合为单个GPU内核
- **混合精度**: 使用FP16和Tensor Core加速
- **异步执行**: 利用GPU流实现计算和传输重叠

### 3. 批处理优化
- **动态形状**: 支持动态批大小和序列长度
- **内存复用**: 在批处理中复用GPU内存
- **负载均衡**: 在多GPU间平衡计算负载

## 测试要求

### 单元测试
- GPU张量操作的正确性验证
- CPU-GPU数据传输测试
- 内存管理测试
- 设备检测和选择测试

### 性能测试
- GPU vs CPU性能对比
- 不同GPU型号的性能基准
- 内存带宽利用率测试
- 批处理性能测试

### 兼容性测试
- 不同CUDA版本的兼容性
- 不同Metal版本的兼容性
- 多GPU环境测试
- 混合设备计算测试

## 验收标准
1. GPU张量操作结果与CPU版本一致
2. 性能显著优于CPU版本（至少5-10倍提升）
3. 内存使用合理，无内存泄漏
4. 支持主流的GPU硬件
5. 跨设备操作稳定可靠
6. 代码文档完整，易于维护

## 优先级
1. **最高优先级**: CUDA基础张量操作、内存管理
2. **高优先级**: Metal基础支持、跨设备传输
3. **中优先级**: 性能优化、多GPU支持
4. **低优先级**: 高级优化特性、专用硬件支持

请确保GPU支持的实现能够无缝集成到现有的推理引擎中，并为用户提供显著的性能提升。
