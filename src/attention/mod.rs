//! Attention mechanisms for transformer models.

pub mod scaled_dot_product;
pub mod multi_head;
pub mod cache;

// pub use scaled_dot_product::*;
// pub use multi_head::*;
// pub use cache::*;

use crate::tensor::{Tensor, Numeric};

/// Base trait for attention mechanisms.
pub trait Attention<T: Numeric>: Send + Sync {
    /// Tensor type used by this attention mechanism.
    type Tensor: Tensor<T>;
    /// Error type for attention operations.
    type Error: std::error::Error + Send + Sync + 'static;
    
    /// Compute attention weights and output.
    fn compute_attention(
        &self,
        query: &Self::Tensor,
        key: &Self::Tensor,
        value: &Self::Tensor,
        mask: Option<&Self::Tensor>,
    ) -> Result<(Self::Tensor, Self::Tensor), Self::Error>; // (output, attention_weights)
    
    /// Get the number of attention heads.
    fn num_heads(&self) -> usize;
    
    /// Get the dimension of each attention head.
    fn head_dim(&self) -> usize;
    
    /// Get the scale factor used in attention computation.
    fn scale_factor(&self) -> T;
}

/// Trait for cached attention (used in autoregressive generation).
pub trait CachedAttention<T: Numeric>: Attention<T> {
    /// Cache type for storing key-value pairs.
    type Cache;
    
    /// Forward pass with key-value cache.
    fn forward_with_cache(
        &self,
        query: &Self::Tensor,
        key: &Self::Tensor,
        value: &Self::Tensor,
        cache: Option<&mut Self::Cache>,
        mask: Option<&Self::Tensor>,
    ) -> Result<(Self::Tensor, Self::Tensor), Self::Error>;
    
    /// Create a new cache for the given batch size and sequence length.
    fn create_cache(&self, batch_size: usize, max_seq_len: usize) -> Self::Cache;
    
    /// Clear the cache.
    fn clear_cache(&self, cache: &mut Self::Cache);
}

/// Configuration for attention mechanisms.
#[derive(Debug, Clone)]
pub struct AttentionConfig {
    /// Hidden dimension.
    pub hidden_size: usize,
    /// Number of attention heads.
    pub num_heads: usize,
    /// Dropout probability.
    pub dropout: f32,
    /// Whether to use bias in linear projections.
    pub use_bias: bool,
    /// Scale factor (if None, uses 1/sqrt(head_dim)).
    pub scale: Option<f32>,
}

impl AttentionConfig {
    /// Create a new attention configuration.
    pub fn new(hidden_size: usize, num_heads: usize) -> Self {
        Self {
            hidden_size,
            num_heads,
            dropout: 0.0,
            use_bias: true,
            scale: None,
        }
    }
    
    /// Get the dimension of each attention head.
    pub fn head_dim(&self) -> usize {
        self.hidden_size / self.num_heads
    }
    
    /// Validate the configuration.
    pub fn validate(&self) -> Result<(), String> {
        if self.hidden_size % self.num_heads != 0 {
            return Err(format!(
                "hidden_size ({}) must be divisible by num_heads ({})",
                self.hidden_size, self.num_heads
            ));
        }
        
        if self.dropout < 0.0 || self.dropout > 1.0 {
            return Err(format!("dropout must be between 0.0 and 1.0, got {}", self.dropout));
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_attention_config() {
        let config = AttentionConfig::new(768, 12);
        assert_eq!(config.hidden_size, 768);
        assert_eq!(config.num_heads, 12);
        assert_eq!(config.head_dim(), 64);
        assert!(config.validate().is_ok());
    }
    
    #[test]
    fn test_attention_config_validation() {
        let mut config = AttentionConfig::new(768, 11); // Not divisible
        assert!(config.validate().is_err());
        
        config.num_heads = 12;
        config.dropout = 1.5; // Invalid dropout
        assert!(config.validate().is_err());
    }
}
