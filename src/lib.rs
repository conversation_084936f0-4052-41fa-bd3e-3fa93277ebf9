//! # Qilin 推理引擎 (麒麟)
//!
//! 一个基于 Rust 的高性能深度学习推理引擎，专为 Transformer 架构设计。
//!
//! 以中国神话中的瑞兽麒麟命名，象征智慧与祥瑞。
//!
//! 本库提供以下功能：
//! - 高效的张量操作，支持 SIMD 优化
//! - 多头注意力机制，支持 KV 缓存
//! - Transformer 编码器/解码器块
//! - 模型配置和管理
//! - 高吞吐量推理引擎
//! - Python 绑定，便于集成
//!
//! ## 特性
//!
//! - **零拷贝操作**：最小化内存分配和数据复制
//! - **SIMD 优化**：向量化操作以获得最大性能
//! - **并行计算**：多线程执行，支持工作窃取
//! - **内存安全**：Rust 的所有权系统防止内存错误
//! - **类型安全**：编译时和运行时形状验证
//! - **设备抽象**：支持 CPU、CUDA 和 Metal 后端
//!
//! ## 快速开始
//!
//! ```rust
//! use qilin_inference::prelude::*;
//!
//! // 创建一个简单的 transformer 配置
//! let config = TransformerConfig::default();
//! println!("Config created with vocab_size: {}", config.vocab_size);
//! # Ok::<(), Box<dyn std::error::Error>>(())
//! ```

#![warn(clippy::all)]
#![allow(clippy::type_complexity)]
#![allow(missing_docs)]

// 核心模块
pub mod error;      // 错误处理
pub mod tensor;     // 张量操作
pub mod layers;     // 神经网络层
pub mod attention;  // 注意力机制
pub mod transformer; // Transformer 架构
pub mod model;      // 模型定义
pub mod inference;  // 推理引擎
pub mod utils;      // 工具函数

// 便于使用的重新导出
pub mod prelude {
    //! 使用推理引擎的常用导入。

    pub use crate::error::*;
    pub use crate::tensor::{Tensor, TensorOps, Shape, DType};
    pub use crate::layers::*;
    pub use crate::attention::*;
    pub use crate::transformer::*;
    pub use crate::model::*;
    pub use crate::inference::*;
    // pub use crate::utils::*;
}

// Optional feature modules
#[cfg(feature = "python-bindings")]
pub mod python;

#[cfg(feature = "cuda")]
pub mod cuda;

#[cfg(feature = "metal")]
pub mod metal;

// Version information
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// Initialize the inference engine with default settings.
///
/// This function sets up logging, thread pools, and other global state.
/// It should be called once at the beginning of your application.
///
/// # Examples
///
/// ```rust
/// use qilin_inference::init;
///
/// fn main() -> Result<(), Box<dyn std::error::Error>> {
///     init()?;
///     // Your application code here
///     Ok(())
/// }
/// ```
pub fn init() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging (ignore error if already initialized)
    let _ = env_logger::try_init();

    // Initialize thread pool (only if not already initialized)
    if rayon::current_num_threads() == 1 {
        rayon::ThreadPoolBuilder::new()
            .num_threads(num_cpus::get())
            .build_global()?;
    }

    log::info!("ComfyUI Inference Engine v{} initialized", VERSION);
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_init() {
        assert!(init().is_ok());
    }

    #[test]
    fn test_version() {
        assert!(!VERSION.is_empty());
    }
}
