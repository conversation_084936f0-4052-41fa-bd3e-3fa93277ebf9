//! Memory pool for efficient tensor memory management.
//!
//! This module provides a memory pool implementation that reduces allocation overhead
//! for tensor operations by reusing memory buffers. This is particularly beneficial for:
//! - Frequent tensor operations in training loops
//! - Real-time inference scenarios
//! - Reducing garbage collection pressure
//! - Minimizing memory fragmentation

use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use crate::tensor::Numeric;
use crate::error::{TensorError, ErrorContext};

/// Configuration for memory pool behavior.
#[derive(Debug, Clone)]
pub struct MemoryPoolConfig {
    /// Maximum number of buffers to keep per size bucket.
    pub max_buffers_per_bucket: usize,
    /// Maximum total memory to keep in the pool (in bytes).
    pub max_total_memory: usize,
    /// Whether to enable memory pool statistics collection.
    pub enable_stats: bool,
    /// Minimum buffer size to pool (smaller buffers are not pooled).
    pub min_pooled_size: usize,
}

impl Default for MemoryPoolConfig {
    fn default() -> Self {
        Self {
            max_buffers_per_bucket: 16,
            max_total_memory: 1024 * 1024 * 1024, // 1GB
            enable_stats: false,
            min_pooled_size: 64, // Don't pool very small buffers
        }
    }
}

/// Statistics for memory pool usage.
#[derive(Debug, Clone, Default)]
pub struct MemoryPoolStats {
    /// Total number of allocations requested.
    pub total_allocations: u64,
    /// Number of allocations served from the pool.
    pub pool_hits: u64,
    /// Number of allocations that required new memory.
    pub pool_misses: u64,
    /// Total number of deallocations.
    pub total_deallocations: u64,
    /// Number of buffers returned to the pool.
    pub pool_returns: u64,
    /// Number of buffers discarded (pool full or too large).
    pub pool_discards: u64,
    /// Current number of buffers in the pool.
    pub current_pooled_buffers: usize,
    /// Current total memory in the pool (bytes).
    pub current_pooled_memory: usize,
}

impl MemoryPoolStats {
    /// Calculate the pool hit rate as a percentage.
    pub fn hit_rate(&self) -> f64 {
        if self.total_allocations == 0 {
            0.0
        } else {
            (self.pool_hits as f64 / self.total_allocations as f64) * 100.0
        }
    }
    
    /// Calculate the pool return rate as a percentage.
    pub fn return_rate(&self) -> f64 {
        if self.total_deallocations == 0 {
            0.0
        } else {
            (self.pool_returns as f64 / self.total_deallocations as f64) * 100.0
        }
    }
}

/// A memory pool for efficient tensor buffer management.
/// 
/// The memory pool maintains buckets of pre-allocated buffers organized by size.
/// This reduces allocation overhead and memory fragmentation for frequent tensor operations.
/// 
/// # Thread Safety
/// The memory pool is thread-safe and can be shared across multiple threads.
/// 
/// # Examples
/// ```rust,ignore
/// use qilin_inference::tensor::memory_pool::{TensorMemoryPool, MemoryPoolConfig};
/// 
/// let config = MemoryPoolConfig::default();
/// let pool = TensorMemoryPool::new(config);
/// 
/// // Allocate a buffer
/// let buffer: Vec<f32> = pool.allocate(1024)?;
/// 
/// // Use the buffer...
/// 
/// // Return the buffer to the pool
/// pool.deallocate(buffer);
/// ```
pub struct TensorMemoryPool<T: Numeric> {
    /// Configuration for the memory pool.
    config: MemoryPoolConfig,
    /// Buckets of buffers organized by size.
    /// Key: buffer size, Value: vector of available buffers
    buckets: Arc<Mutex<HashMap<usize, Vec<Vec<T>>>>>,
    /// Statistics for pool usage.
    stats: Arc<Mutex<MemoryPoolStats>>,
    /// Current total memory in the pool.
    current_memory: Arc<Mutex<usize>>,
}

impl<T: Numeric> TensorMemoryPool<T> {
    /// Create a new memory pool with the given configuration.
    pub fn new(config: MemoryPoolConfig) -> Self {
        Self {
            config,
            buckets: Arc::new(Mutex::new(HashMap::new())),
            stats: Arc::new(Mutex::new(MemoryPoolStats::default())),
            current_memory: Arc::new(Mutex::new(0)),
        }
    }
    
    /// Create a new memory pool with default configuration.
    pub fn default() -> Self {
        Self::new(MemoryPoolConfig::default())
    }
    
    /// Allocate a buffer of the specified size.
    /// 
    /// This will first try to reuse a buffer from the pool. If no suitable buffer
    /// is available, a new buffer will be allocated.
    /// 
    /// # Arguments
    /// * `size` - The number of elements to allocate
    /// 
    /// # Returns
    /// A vector with the requested capacity, initialized with zeros.
    pub fn allocate(&self, size: usize) -> Result<Vec<T>, TensorError> {
        if self.config.enable_stats {
            let mut stats = self.stats.lock().unwrap();
            stats.total_allocations += 1;
        }
        
        // Don't pool very small buffers
        if size < self.config.min_pooled_size {
            if self.config.enable_stats {
                let mut stats = self.stats.lock().unwrap();
                stats.pool_misses += 1;
            }
            return Ok(vec![T::ZERO; size]);
        }
        
        // Try to get a buffer from the pool
        let mut buckets = self.buckets.lock().unwrap();
        if let Some(bucket) = buckets.get_mut(&size) {
            if let Some(mut buffer) = bucket.pop() {
                // Update statistics
                if self.config.enable_stats {
                    let mut stats = self.stats.lock().unwrap();
                    stats.pool_hits += 1;
                    stats.current_pooled_buffers -= 1;
                }
                
                // Update current memory
                let mut current_memory = self.current_memory.lock().unwrap();
                *current_memory -= size * std::mem::size_of::<T>();
                
                // Ensure the buffer is the right size and zeroed
                buffer.resize(size, T::ZERO);
                buffer.fill(T::ZERO);
                
                return Ok(buffer);
            }
        }
        
        // No buffer available in pool, allocate new one
        if self.config.enable_stats {
            let mut stats = self.stats.lock().unwrap();
            stats.pool_misses += 1;
        }
        
        Ok(vec![T::ZERO; size])
    }
    
    /// Return a buffer to the pool for reuse.
    /// 
    /// The buffer will be added to the appropriate size bucket if there's space.
    /// If the pool is full or the buffer is too large, it will be discarded.
    /// 
    /// # Arguments
    /// * `buffer` - The buffer to return to the pool
    pub fn deallocate(&self, buffer: Vec<T>) {
        if self.config.enable_stats {
            let mut stats = self.stats.lock().unwrap();
            stats.total_deallocations += 1;
        }
        
        let size = buffer.len();
        
        // Don't pool very small buffers
        if size < self.config.min_pooled_size {
            if self.config.enable_stats {
                let mut stats = self.stats.lock().unwrap();
                stats.pool_discards += 1;
            }
            return;
        }
        
        let buffer_memory = size * std::mem::size_of::<T>();
        
        // Check if we can add this buffer to the pool
        let mut current_memory = self.current_memory.lock().unwrap();
        if *current_memory + buffer_memory > self.config.max_total_memory {
            if self.config.enable_stats {
                let mut stats = self.stats.lock().unwrap();
                stats.pool_discards += 1;
            }
            return;
        }
        
        let mut buckets = self.buckets.lock().unwrap();
        let bucket = buckets.entry(size).or_insert_with(Vec::new);
        
        // Check if this bucket is full
        if bucket.len() >= self.config.max_buffers_per_bucket {
            if self.config.enable_stats {
                let mut stats = self.stats.lock().unwrap();
                stats.pool_discards += 1;
            }
            return;
        }
        
        // Add buffer to pool
        bucket.push(buffer);
        *current_memory += buffer_memory;
        
        // Update statistics
        if self.config.enable_stats {
            let mut stats = self.stats.lock().unwrap();
            stats.pool_returns += 1;
            stats.current_pooled_buffers += 1;
            stats.current_pooled_memory = *current_memory;
        }
    }
    
    /// Get current memory pool statistics.
    pub fn stats(&self) -> MemoryPoolStats {
        if self.config.enable_stats {
            self.stats.lock().unwrap().clone()
        } else {
            MemoryPoolStats::default()
        }
    }
    
    /// Clear all buffers from the pool.
    /// 
    /// This frees all pooled memory and resets statistics.
    pub fn clear(&self) {
        let mut buckets = self.buckets.lock().unwrap();
        buckets.clear();
        
        let mut current_memory = self.current_memory.lock().unwrap();
        *current_memory = 0;
        
        if self.config.enable_stats {
            let mut stats = self.stats.lock().unwrap();
            stats.current_pooled_buffers = 0;
            stats.current_pooled_memory = 0;
        }
    }
    
    /// Get the current configuration.
    pub fn config(&self) -> &MemoryPoolConfig {
        &self.config
    }
    
    /// Get the number of buffers currently in the pool.
    pub fn pooled_buffer_count(&self) -> usize {
        let buckets = self.buckets.lock().unwrap();
        buckets.values().map(|bucket| bucket.len()).sum()
    }
    
    /// Get the current memory usage of the pool in bytes.
    pub fn current_memory_usage(&self) -> usize {
        *self.current_memory.lock().unwrap()
    }
}

impl<T: Numeric> Clone for TensorMemoryPool<T> {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            buckets: Arc::clone(&self.buckets),
            stats: Arc::clone(&self.stats),
            current_memory: Arc::clone(&self.current_memory),
        }
    }
}

/// Global memory pool instance for convenient access.
/// 
/// This provides a default memory pool that can be used throughout the application
/// without needing to pass pool instances around.
pub struct GlobalMemoryPool;

impl GlobalMemoryPool {
    /// Get the global memory pool for f32 tensors.
    pub fn f32() -> &'static TensorMemoryPool<f32> {
        static POOL: std::sync::OnceLock<TensorMemoryPool<f32>> = std::sync::OnceLock::new();
        POOL.get_or_init(|| TensorMemoryPool::new(MemoryPoolConfig::default()))
    }
    
    /// Get the global memory pool for f64 tensors.
    pub fn f64() -> &'static TensorMemoryPool<f64> {
        static POOL: std::sync::OnceLock<TensorMemoryPool<f64>> = std::sync::OnceLock::new();
        POOL.get_or_init(|| TensorMemoryPool::new(MemoryPoolConfig::default()))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_memory_pool_basic() {
        let mut config = MemoryPoolConfig::default();
        config.enable_stats = true;
        let pool = TensorMemoryPool::<f32>::new(config);
        
        // Allocate a buffer
        let buffer = pool.allocate(1024).unwrap();
        assert_eq!(buffer.len(), 1024);
        assert!(buffer.iter().all(|&x| x == 0.0));
        
        // Return it to the pool
        pool.deallocate(buffer);
        
        // Allocate again - should come from pool
        let buffer2 = pool.allocate(1024).unwrap();
        assert_eq!(buffer2.len(), 1024);
        
        let stats = pool.stats();
        assert_eq!(stats.total_allocations, 2);
        assert_eq!(stats.pool_hits, 1);
        assert_eq!(stats.pool_misses, 1);
    }
    
    #[test]
    fn test_memory_pool_size_buckets() {
        let pool = TensorMemoryPool::<f32>::new(MemoryPoolConfig::default());
        
        // Allocate buffers of different sizes
        let buffer1 = pool.allocate(512).unwrap();
        let buffer2 = pool.allocate(1024).unwrap();
        let buffer3 = pool.allocate(512).unwrap();
        
        // Return them
        pool.deallocate(buffer1);
        pool.deallocate(buffer2);
        pool.deallocate(buffer3);
        
        // Should have 2 buffers of size 512 and 1 of size 1024
        assert_eq!(pool.pooled_buffer_count(), 3);
    }
    
    #[test]
    fn test_memory_pool_limits() {
        let mut config = MemoryPoolConfig::default();
        config.max_buffers_per_bucket = 2;
        config.enable_stats = true;
        let pool = TensorMemoryPool::<f32>::new(config);

        // Allocate 3 buffers of the same size
        let buffer1 = pool.allocate(1024).unwrap();
        let buffer2 = pool.allocate(1024).unwrap();
        let buffer3 = pool.allocate(1024).unwrap();

        // Return them all to the pool
        pool.deallocate(buffer1); // Goes to pool (1 in pool)
        pool.deallocate(buffer2); // Goes to pool (2 in pool)
        pool.deallocate(buffer3); // Should be discarded (pool full)

        // Should only have 2 buffers in pool (max_buffers_per_bucket = 2)
        assert_eq!(pool.pooled_buffer_count(), 2);

        let stats = pool.stats();
        assert_eq!(stats.pool_discards, 1);
    }
}
