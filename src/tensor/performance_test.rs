//! Performance tests for optimized tensor operations.
//!
//! This module provides performance comparison tests between sequential,
//! SIMD, parallel, and optimized implementations.

use crate::tensor::{
    simd::SimdOps,
    parallel::{ParallelOps, ParallelConfig},
    optimized::{OptimizedOps, OptimizationConfig, OptimizationStrategy},
};
use std::time::Instant;

/// Performance test configuration.
#[derive(Debug, Clone)]
pub struct PerformanceTestConfig {
    /// Number of iterations for each test.
    pub iterations: usize,
    /// Sizes to test.
    pub test_sizes: Vec<usize>,
    /// Whether to print detailed results.
    pub verbose: bool,
}

impl Default for PerformanceTestConfig {
    fn default() -> Self {
        Self {
            iterations: 100,
            test_sizes: vec![100, 1_000, 10_000, 100_000],
            verbose: false,
        }
    }
}

/// Performance test results.
#[derive(Debug, Clone)]
pub struct PerformanceResult {
    pub operation: String,
    pub size: usize,
    pub sequential_time_ns: u64,
    pub simd_time_ns: Option<u64>,
    pub parallel_time_ns: Option<u64>,
    pub optimized_time_ns: u64,
}

impl PerformanceResult {
    /// Calculate speedup compared to sequential implementation.
    pub fn speedup(&self, implementation: &str) -> Option<f64> {
        let time_ns = match implementation {
            "simd" => self.simd_time_ns?,
            "parallel" => self.parallel_time_ns?,
            "optimized" => self.optimized_time_ns,
            _ => return None,
        };

        Some(self.sequential_time_ns as f64 / time_ns as f64)
    }
    
    /// Print performance summary.
    pub fn print_summary(&self) {
        println!("Operation: {} (size: {})", self.operation, self.size);
        println!("  Sequential: {} ns", self.sequential_time_ns);
        
        if let Some(simd_time) = self.simd_time_ns {
            println!("  SIMD:       {} ns ({}x speedup)", 
                simd_time, 
                self.speedup("simd").unwrap_or(0.0)
            );
        }
        
        if let Some(parallel_time) = self.parallel_time_ns {
            println!("  Parallel:   {} ns ({}x speedup)", 
                parallel_time, 
                self.speedup("parallel").unwrap_or(0.0)
            );
        }
        
        println!("  Optimized:  {} ns ({}x speedup)", 
            self.optimized_time_ns, 
            self.speedup("optimized").unwrap_or(0.0)
        );
        println!();
    }
}

/// Performance testing utilities.
pub struct PerformanceTester;

impl PerformanceTester {
    /// Benchmark element-wise addition.
    pub fn benchmark_add_f32(size: usize, iterations: usize) -> PerformanceResult {
        let a: Vec<f32> = (0..size).map(|i| i as f32).collect();
        let b: Vec<f32> = (0..size).map(|i| (size - i) as f32).collect();
        
        // Sequential benchmark
        let start = Instant::now();
        for _ in 0..iterations {
            let mut result = vec![0.0; size];
            for ((a_val, b_val), res) in a.iter().zip(b.iter()).zip(result.iter_mut()) {
                *res = a_val + b_val;
            }
        }
        let sequential_time_ns = start.elapsed().as_nanos() as u64 / iterations as u64;
        
        // SIMD benchmark
        let simd_time_ns = if size >= 8 {
            let start = Instant::now();
            for _ in 0..iterations {
                let mut result = vec![0.0; size];
                #[cfg(feature = "simd")]
                SimdOps::add_f32(&a, &b, &mut result);
                #[cfg(not(feature = "simd"))]
                {
                    for ((a_val, b_val), res) in a.iter().zip(b.iter()).zip(result.iter_mut()) {
                        *res = a_val + b_val;
                    }
                }
            }
            Some(start.elapsed().as_nanos() as u64 / iterations as u64)
        } else {
            None
        };
        
        // Parallel benchmark
        let parallel_time_ns = if size >= 1000 {
            let config = ParallelConfig::new(1000, None, 100);
            let start = Instant::now();
            for _ in 0..iterations {
                let mut result = vec![0.0; size];
                ParallelOps::add_f32_parallel(&a, &b, &mut result, &config);
            }
            Some(start.elapsed().as_nanos() as u64 / iterations as u64)
        } else {
            None
        };
        
        // Optimized benchmark
        let opt_config = OptimizationConfig::default();
        let start = Instant::now();
        for _ in 0..iterations {
            let mut result = vec![0.0; size];
            OptimizedOps::add_f32_optimized(&a, &b, &mut result, &opt_config);
        }
        let optimized_time_ns = start.elapsed().as_nanos() as u64 / iterations as u64;
        
        PerformanceResult {
            operation: "add_f32".to_string(),
            size,
            sequential_time_ns,
            simd_time_ns,
            parallel_time_ns,
            optimized_time_ns,
        }
    }
    
    /// Benchmark dot product.
    pub fn benchmark_dot_product_f32(size: usize, iterations: usize) -> PerformanceResult {
        let a: Vec<f32> = (0..size).map(|i| i as f32).collect();
        let b: Vec<f32> = (0..size).map(|i| (size - i) as f32).collect();
        
        // Sequential benchmark
        let start = Instant::now();
        for _ in 0..iterations {
            let _result: f32 = a.iter().zip(b.iter()).map(|(a_val, b_val)| a_val * b_val).sum();
        }
        let sequential_time_ns = start.elapsed().as_nanos() as u64 / iterations as u64;
        
        // SIMD benchmark
        let simd_time_ns = if size >= 8 {
            let start = Instant::now();
            for _ in 0..iterations {
                #[cfg(feature = "simd")]
                let _result = SimdOps::dot_product_f32(&a, &b);
                #[cfg(not(feature = "simd"))]
                let _result: f32 = a.iter().zip(b.iter()).map(|(a_val, b_val)| a_val * b_val).sum();
            }
            Some(start.elapsed().as_nanos() as u64 / iterations as u64)
        } else {
            None
        };
        
        // Parallel benchmark
        let parallel_time_ns = if size >= 1000 {
            let config = ParallelConfig::new(1000, None, 100);
            let start = Instant::now();
            for _ in 0..iterations {
                let _result = ParallelOps::dot_product_f32_parallel(&a, &b, &config);
            }
            Some(start.elapsed().as_nanos() as u64 / iterations as u64)
        } else {
            None
        };
        
        // Optimized benchmark
        let opt_config = OptimizationConfig::default();
        let start = Instant::now();
        for _ in 0..iterations {
            let _result = OptimizedOps::dot_product_f32_optimized(&a, &b, &opt_config);
        }
        let optimized_time_ns = start.elapsed().as_nanos() as u64 / iterations as u64;
        
        PerformanceResult {
            operation: "dot_product_f32".to_string(),
            size,
            sequential_time_ns,
            simd_time_ns,
            parallel_time_ns,
            optimized_time_ns,
        }
    }
    
    /// Run comprehensive performance tests.
    pub fn run_comprehensive_tests(config: &PerformanceTestConfig) -> Vec<PerformanceResult> {
        let mut results = Vec::new();
        
        for &size in &config.test_sizes {
            if config.verbose {
                println!("Testing size: {}", size);
            }
            
            // Test addition
            let add_result = Self::benchmark_add_f32(size, config.iterations);
            if config.verbose {
                add_result.print_summary();
            }
            results.push(add_result);
            
            // Test dot product
            let dot_result = Self::benchmark_dot_product_f32(size, config.iterations);
            if config.verbose {
                dot_result.print_summary();
            }
            results.push(dot_result);
        }
        
        results
    }
    
    /// Verify that all implementations produce the same results.
    pub fn verify_correctness(size: usize) -> bool {
        let a: Vec<f32> = (0..size).map(|i| (i as f32) * 0.1).collect();
        let b: Vec<f32> = (0..size).map(|i| ((size - i) as f32) * 0.1).collect();

        // Sequential results
        let mut seq_add = vec![0.0; size];
        for ((a_val, b_val), res) in a.iter().zip(b.iter()).zip(seq_add.iter_mut()) {
            *res = a_val + b_val;
        }
        let seq_dot: f32 = a.iter().zip(b.iter()).map(|(a_val, b_val)| a_val * b_val).sum();

        // SIMD results (only test if size is large enough)
        #[cfg(feature = "simd")]
        if size >= 8 {
            let mut simd_add = vec![0.0; size];
            SimdOps::add_f32(&a, &b, &mut simd_add);
            let simd_dot = SimdOps::dot_product_f32(&a, &b);

            // Check SIMD correctness with relative tolerance for floating point errors
            for (seq, simd) in seq_add.iter().zip(simd_add.iter()) {
                let rel_error = if seq.abs() > 1e-6 {
                    (seq - simd).abs() / seq.abs()
                } else {
                    (seq - simd).abs()
                };
                if rel_error > 1e-4 {
                    eprintln!("SIMD add mismatch: {} vs {} (rel_error: {})", seq, simd, rel_error);
                    return false;
                }
            }
            let dot_rel_error = if seq_dot.abs() > 1e-6 {
                (seq_dot - simd_dot).abs() / seq_dot.abs()
            } else {
                (seq_dot - simd_dot).abs()
            };
            if dot_rel_error > 1e-4 {
                eprintln!("SIMD dot mismatch: {} vs {} (rel_error: {})", seq_dot, simd_dot, dot_rel_error);
                return false;
            }
        }

        // Parallel results (only test if size is large enough)
        if size >= 1000 {
            let config = ParallelConfig::new(500, None, 100); // Lower threshold for testing
            let mut par_add = vec![0.0; size];
            ParallelOps::add_f32_parallel(&a, &b, &mut par_add, &config);
            let par_dot = ParallelOps::dot_product_f32_parallel(&a, &b, &config);

            // Check parallel correctness with relative tolerance
            for (seq, par) in seq_add.iter().zip(par_add.iter()) {
                let rel_error = if seq.abs() > 1e-6 {
                    (seq - par).abs() / seq.abs()
                } else {
                    (seq - par).abs()
                };
                if rel_error > 1e-4 {
                    eprintln!("Parallel add mismatch: {} vs {} (rel_error: {})", seq, par, rel_error);
                    return false;
                }
            }
            let dot_rel_error = if seq_dot.abs() > 1e-6 {
                (seq_dot - par_dot).abs() / seq_dot.abs()
            } else {
                (seq_dot - par_dot).abs()
            };
            if dot_rel_error > 1e-4 {
                eprintln!("Parallel dot mismatch: {} vs {} (rel_error: {})", seq_dot, par_dot, dot_rel_error);
                return false;
            }
        }

        // Optimized results
        let opt_config = OptimizationConfig::new(
            ParallelConfig::new(50_000, None, 1000), // High threshold to avoid parallel for small tests
            64,
            true
        );
        let mut opt_add = vec![0.0; size];
        OptimizedOps::add_f32_optimized(&a, &b, &mut opt_add, &opt_config);
        let opt_dot = OptimizedOps::dot_product_f32_optimized(&a, &b, &opt_config);

        // Check optimized correctness with relative tolerance
        for (seq, opt) in seq_add.iter().zip(opt_add.iter()) {
            let rel_error = if seq.abs() > 1e-6 {
                (seq - opt).abs() / seq.abs()
            } else {
                (seq - opt).abs()
            };
            if rel_error > 1e-4 {
                eprintln!("Optimized add mismatch: {} vs {} (rel_error: {})", seq, opt, rel_error);
                return false;
            }
        }
        let dot_rel_error = if seq_dot.abs() > 1e-6 {
            (seq_dot - opt_dot).abs() / seq_dot.abs()
        } else {
            (seq_dot - opt_dot).abs()
        };
        if dot_rel_error > 1e-4 {
            eprintln!("Optimized dot mismatch: {} vs {} (rel_error: {})", seq_dot, opt_dot, dot_rel_error);
            return false;
        }

        true
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_performance_correctness() {
        // Test correctness for different sizes
        assert!(PerformanceTester::verify_correctness(100));
        assert!(PerformanceTester::verify_correctness(1_000));
        assert!(PerformanceTester::verify_correctness(10_000));
    }

    #[test]
    fn test_performance_benchmark() {
        // Run a quick performance test
        let config = PerformanceTestConfig {
            iterations: 10,
            test_sizes: vec![100, 1_000],
            verbose: false,
        };
        
        let results = PerformanceTester::run_comprehensive_tests(&config);
        assert_eq!(results.len(), 4); // 2 sizes * 2 operations
        
        // Verify all results have reasonable timing
        for result in results {
            assert!(result.sequential_time_ns > 0);
            assert!(result.optimized_time_ns > 0);
            
            // Optimized should not be significantly slower than sequential
            let slowdown = result.optimized_time_ns as f64 / result.sequential_time_ns as f64;
            assert!(slowdown < 10.0, "Optimized implementation is too slow: {}x", slowdown);
        }
    }

    #[test]
    fn test_performance_result_speedup() {
        let result = PerformanceResult {
            operation: "test".to_string(),
            size: 1000,
            sequential_time_ns: 1000,
            simd_time_ns: Some(500),
            parallel_time_ns: Some(250),
            optimized_time_ns: 200,
        };
        
        assert_eq!(result.speedup("simd"), Some(2.0));
        assert_eq!(result.speedup("parallel"), Some(4.0));
        assert_eq!(result.speedup("optimized"), Some(5.0));
        assert_eq!(result.speedup("unknown"), None);
    }
}
