//! SIMD optimizations for tensor operations.
//!
//! This module provides SIMD-accelerated implementations of common tensor operations
//! using the `wide` crate for cross-platform SIMD support. SIMD (Single Instruction,
//! Multiple Data) allows processing multiple data elements simultaneously, providing
//! significant performance improvements for large tensor operations.
//!
//! # Features
//!
//! - Cross-platform SIMD support using the `wide` crate
//! - Optimized for f32 operations with f32x8 vectors (8 elements at once)
//! - Fallback to scalar operations for remaining elements
//! - Memory-safe implementations with proper bounds checking
//!
//! # Performance
//!
//! SIMD operations can provide 2-8x performance improvements over scalar operations,
//! depending on the operation type and data size. The performance gain is most
//! significant for large tensors (>1000 elements).
//!
//! # Examples
//!
//! ```rust
//! # #[cfg(feature = "simd")]
//! # {
//! use qilin_inference::tensor::simd::SimdOps;
//!
//! let a = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0];
//! let b = vec![2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0];
//! let mut result = vec![0.0; 8];
//!
//! // SIMD-optimized addition
//! SimdOps::add_f32(&a, &b, &mut result);
//! // result: [3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0]
//!
//! // SIMD-optimized multiplication
//! SimdOps::mul_f32(&a, &b, &mut result);
//! // result: [2.0, 4.0, 6.0, 8.0, 10.0, 12.0, 14.0, 16.0]
//! # }
//! ```

use crate::tensor::Numeric;

#[cfg(feature = "simd")]
use wide::{f32x4, f32x8};

/// SIMD-optimized operations for f32 tensors.
///
/// This struct provides static methods for SIMD-accelerated tensor operations.
/// All methods are optimized to process 8 f32 elements simultaneously using
/// f32x8 SIMD vectors, with scalar fallback for remaining elements.
///
/// # Thread Safety
///
/// All methods are thread-safe and can be called concurrently from multiple threads.
///
/// # Memory Requirements
///
/// Input slices must have the same length, and the result slice must be pre-allocated
/// with the same length as the input slices.
pub struct SimdOps;

impl SimdOps {
    /// SIMD-optimized element-wise addition for f32 slices.
    ///
    /// Performs element-wise addition of two f32 slices using SIMD instructions.
    /// Processes 8 elements at a time using f32x8 vectors for maximum performance.
    ///
    /// # Arguments
    ///
    /// * `a` - First input slice
    /// * `b` - Second input slice
    /// * `result` - Output slice to store the results
    ///
    /// # Panics
    ///
    /// Panics if the input slices have different lengths or if the result slice
    /// has a different length than the input slices.
    ///
    /// # Performance
    ///
    /// This function provides significant performance improvements over scalar addition:
    /// - ~4-8x faster for large arrays (>1000 elements)
    /// - Optimal performance when array length is divisible by 8
    /// - Automatic fallback to scalar operations for remaining elements
    ///
    /// # Examples
    ///
    /// ```rust
    /// # #[cfg(feature = "simd")]
    /// # {
    /// use qilin_inference::tensor::simd::SimdOps;
    ///
    /// let a = vec![1.0, 2.0, 3.0, 4.0];
    /// let b = vec![5.0, 6.0, 7.0, 8.0];
    /// let mut result = vec![0.0; 4];
    ///
    /// SimdOps::add_f32(&a, &b, &mut result);
    /// assert_eq!(result, vec![6.0, 8.0, 10.0, 12.0]);
    /// # }
    /// ```
    #[cfg(feature = "simd")]
    pub fn add_f32(a: &[f32], b: &[f32], result: &mut [f32]) {
        assert_eq!(a.len(), b.len());
        assert_eq!(a.len(), result.len());
        
        let len = a.len();
        let simd_len = len - (len % 8);
        
        // Process 8 elements at a time using f32x8
        for i in (0..simd_len).step_by(8) {
            let a_chunk = f32x8::from([a[i], a[i+1], a[i+2], a[i+3], a[i+4], a[i+5], a[i+6], a[i+7]]);
            let b_chunk = f32x8::from([b[i], b[i+1], b[i+2], b[i+3], b[i+4], b[i+5], b[i+6], b[i+7]]);
            let sum = a_chunk + b_chunk;
            let sum_array: [f32; 8] = sum.into();
            result[i..i + 8].copy_from_slice(&sum_array);
        }
        
        // Handle remaining elements
        for i in simd_len..len {
            result[i] = a[i] + b[i];
        }
    }
    
    /// SIMD-optimized element-wise multiplication for f32 slices.
    #[cfg(feature = "simd")]
    pub fn mul_f32(a: &[f32], b: &[f32], result: &mut [f32]) {
        assert_eq!(a.len(), b.len());
        assert_eq!(a.len(), result.len());
        
        let len = a.len();
        let simd_len = len - (len % 8);
        
        // Process 8 elements at a time using f32x8
        for i in (0..simd_len).step_by(8) {
            let a_chunk = f32x8::from([a[i], a[i+1], a[i+2], a[i+3], a[i+4], a[i+5], a[i+6], a[i+7]]);
            let b_chunk = f32x8::from([b[i], b[i+1], b[i+2], b[i+3], b[i+4], b[i+5], b[i+6], b[i+7]]);
            let product = a_chunk * b_chunk;
            let product_array: [f32; 8] = product.into();
            result[i..i + 8].copy_from_slice(&product_array);
        }
        
        // Handle remaining elements
        for i in simd_len..len {
            result[i] = a[i] * b[i];
        }
    }
    
    /// SIMD-optimized scalar addition for f32 slices.
    #[cfg(feature = "simd")]
    pub fn add_scalar_f32(a: &[f32], scalar: f32, result: &mut [f32]) {
        assert_eq!(a.len(), result.len());
        
        let len = a.len();
        let simd_len = len - (len % 8);
        let scalar_vec = f32x8::splat(scalar);
        
        // Process 8 elements at a time
        for i in (0..simd_len).step_by(8) {
            let a_chunk = f32x8::from([a[i], a[i+1], a[i+2], a[i+3], a[i+4], a[i+5], a[i+6], a[i+7]]);
            let sum = a_chunk + scalar_vec;
            let sum_array: [f32; 8] = sum.into();
            result[i..i + 8].copy_from_slice(&sum_array);
        }
        
        // Handle remaining elements
        for i in simd_len..len {
            result[i] = a[i] + scalar;
        }
    }
    
    /// SIMD-optimized scalar multiplication for f32 slices.
    #[cfg(feature = "simd")]
    pub fn mul_scalar_f32(a: &[f32], scalar: f32, result: &mut [f32]) {
        assert_eq!(a.len(), result.len());
        
        let len = a.len();
        let simd_len = len - (len % 8);
        let scalar_vec = f32x8::splat(scalar);
        
        // Process 8 elements at a time
        for i in (0..simd_len).step_by(8) {
            let a_chunk = f32x8::from([a[i], a[i+1], a[i+2], a[i+3], a[i+4], a[i+5], a[i+6], a[i+7]]);
            let product = a_chunk * scalar_vec;
            let product_array: [f32; 8] = product.into();
            result[i..i + 8].copy_from_slice(&product_array);
        }
        
        // Handle remaining elements
        for i in simd_len..len {
            result[i] = a[i] * scalar;
        }
    }
    
    /// SIMD-optimized dot product for f32 slices.
    #[cfg(feature = "simd")]
    pub fn dot_product_f32(a: &[f32], b: &[f32]) -> f32 {
        assert_eq!(a.len(), b.len());
        
        let len = a.len();
        let simd_len = len - (len % 8);
        let mut sum_vec = f32x8::splat(0.0);
        
        // Process 8 elements at a time
        for i in (0..simd_len).step_by(8) {
            let a_chunk = f32x8::from([a[i], a[i+1], a[i+2], a[i+3], a[i+4], a[i+5], a[i+6], a[i+7]]);
            let b_chunk = f32x8::from([b[i], b[i+1], b[i+2], b[i+3], b[i+4], b[i+5], b[i+6], b[i+7]]);
            sum_vec += a_chunk * b_chunk;
        }
        
        // Sum the SIMD vector elements
        let sum_array: [f32; 8] = sum_vec.into();
        let mut result = sum_array.iter().sum::<f32>();
        
        // Handle remaining elements
        for i in simd_len..len {
            result += a[i] * b[i];
        }
        
        result
    }
    
    /// SIMD-optimized sum reduction for f32 slices.
    #[cfg(feature = "simd")]
    pub fn sum_f32(a: &[f32]) -> f32 {
        let len = a.len();
        let simd_len = len - (len % 8);
        let mut sum_vec = f32x8::splat(0.0);
        
        // Process 8 elements at a time
        for i in (0..simd_len).step_by(8) {
            let a_chunk = f32x8::from([a[i], a[i+1], a[i+2], a[i+3], a[i+4], a[i+5], a[i+6], a[i+7]]);
            sum_vec += a_chunk;
        }
        
        // Sum the SIMD vector elements
        let sum_array: [f32; 8] = sum_vec.into();
        let mut result = sum_array.iter().sum::<f32>();
        
        // Handle remaining elements
        for i in simd_len..len {
            result += a[i];
        }
        
        result
    }
    
    /// SIMD-optimized exponential function for f32 slices.
    #[cfg(feature = "simd")]
    pub fn exp_f32(a: &[f32], result: &mut [f32]) {
        assert_eq!(a.len(), result.len());
        
        let len = a.len();
        let simd_len = len - (len % 4); // Use f32x4 for exp (more stable)
        
        // Process 4 elements at a time using f32x4
        for i in (0..simd_len).step_by(4) {
            let a_chunk = f32x4::from([a[i], a[i+1], a[i+2], a[i+3]]);
            let exp_result = a_chunk.exp();
            let exp_array: [f32; 4] = exp_result.into();
            result[i..i + 4].copy_from_slice(&exp_array);
        }
        
        // Handle remaining elements
        for i in simd_len..len {
            result[i] = a[i].exp();
        }
    }
    
    /// SIMD-optimized sine function for f32 slices.
    #[cfg(feature = "simd")]
    pub fn sin_f32(a: &[f32], result: &mut [f32]) {
        assert_eq!(a.len(), result.len());
        
        let len = a.len();
        let simd_len = len - (len % 4); // Use f32x4 for trigonometric functions
        
        // Process 4 elements at a time using f32x4
        for i in (0..simd_len).step_by(4) {
            let a_chunk = f32x4::from([a[i], a[i+1], a[i+2], a[i+3]]);
            let sin_result = a_chunk.sin();
            let sin_array: [f32; 4] = sin_result.into();
            result[i..i + 4].copy_from_slice(&sin_array);
        }
        
        // Handle remaining elements
        for i in simd_len..len {
            result[i] = a[i].sin();
        }
    }
    
    /// SIMD-optimized cosine function for f32 slices.
    #[cfg(feature = "simd")]
    pub fn cos_f32(a: &[f32], result: &mut [f32]) {
        assert_eq!(a.len(), result.len());
        
        let len = a.len();
        let simd_len = len - (len % 4); // Use f32x4 for trigonometric functions
        
        // Process 4 elements at a time using f32x4
        for i in (0..simd_len).step_by(4) {
            let a_chunk = f32x4::from([a[i], a[i+1], a[i+2], a[i+3]]);
            let cos_result = a_chunk.cos();
            let cos_array: [f32; 4] = cos_result.into();
            result[i..i + 4].copy_from_slice(&cos_array);
        }
        
        // Handle remaining elements
        for i in simd_len..len {
            result[i] = a[i].cos();
        }
    }
}

// Fallback implementations when SIMD is not available
#[cfg(not(feature = "simd"))]
impl SimdOps {
    pub fn add_f32(a: &[f32], b: &[f32], result: &mut [f32]) {
        for ((a_val, b_val), res) in a.iter().zip(b.iter()).zip(result.iter_mut()) {
            *res = a_val + b_val;
        }
    }
    
    pub fn mul_f32(a: &[f32], b: &[f32], result: &mut [f32]) {
        for ((a_val, b_val), res) in a.iter().zip(b.iter()).zip(result.iter_mut()) {
            *res = a_val * b_val;
        }
    }
    
    pub fn add_scalar_f32(a: &[f32], scalar: f32, result: &mut [f32]) {
        for (a_val, res) in a.iter().zip(result.iter_mut()) {
            *res = a_val + scalar;
        }
    }
    
    pub fn mul_scalar_f32(a: &[f32], scalar: f32, result: &mut [f32]) {
        for (a_val, res) in a.iter().zip(result.iter_mut()) {
            *res = a_val * scalar;
        }
    }
    
    pub fn dot_product_f32(a: &[f32], b: &[f32]) -> f32 {
        a.iter().zip(b.iter()).map(|(a_val, b_val)| a_val * b_val).sum()
    }
    
    pub fn sum_f32(a: &[f32]) -> f32 {
        a.iter().sum()
    }
    
    pub fn exp_f32(a: &[f32], result: &mut [f32]) {
        for (a_val, res) in a.iter().zip(result.iter_mut()) {
            *res = a_val.exp();
        }
    }
    
    pub fn sin_f32(a: &[f32], result: &mut [f32]) {
        for (a_val, res) in a.iter().zip(result.iter_mut()) {
            *res = a_val.sin();
        }
    }
    
    pub fn cos_f32(a: &[f32], result: &mut [f32]) {
        for (a_val, res) in a.iter().zip(result.iter_mut()) {
            *res = a_val.cos();
        }
    }
}

/// Trait for SIMD-optimized operations on numeric types.
pub trait SimdOptimized<T: Numeric> {
    /// SIMD-optimized element-wise addition.
    fn simd_add(&self, other: &[T], result: &mut [T]);
    
    /// SIMD-optimized element-wise multiplication.
    fn simd_mul(&self, other: &[T], result: &mut [T]);
    
    /// SIMD-optimized scalar addition.
    fn simd_add_scalar(&self, scalar: T, result: &mut [T]);
    
    /// SIMD-optimized scalar multiplication.
    fn simd_mul_scalar(&self, scalar: T, result: &mut [T]);
    
    /// SIMD-optimized dot product.
    fn simd_dot_product(&self, other: &[T]) -> T;
    
    /// SIMD-optimized sum reduction.
    fn simd_sum(&self) -> T;
}

impl SimdOptimized<f32> for [f32] {
    fn simd_add(&self, other: &[f32], result: &mut [f32]) {
        SimdOps::add_f32(self, other, result);
    }
    
    fn simd_mul(&self, other: &[f32], result: &mut [f32]) {
        SimdOps::mul_f32(self, other, result);
    }
    
    fn simd_add_scalar(&self, scalar: f32, result: &mut [f32]) {
        SimdOps::add_scalar_f32(self, scalar, result);
    }
    
    fn simd_mul_scalar(&self, scalar: f32, result: &mut [f32]) {
        SimdOps::mul_scalar_f32(self, scalar, result);
    }
    
    fn simd_dot_product(&self, other: &[f32]) -> f32 {
        SimdOps::dot_product_f32(self, other)
    }
    
    fn simd_sum(&self) -> f32 {
        SimdOps::sum_f32(self)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use approx::assert_relative_eq;

    #[test]
    fn test_simd_add_f32() {
        let a = [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0];
        let b = [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 6.5, 7.5, 8.5, 9.5];
        let mut result = [0.0; 10];
        let expected = [1.5, 3.5, 5.5, 7.5, 9.5, 11.5, 13.5, 15.5, 17.5, 19.5];

        SimdOps::add_f32(&a, &b, &mut result);

        for (&res, &exp) in result.iter().zip(expected.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }
    }

    #[test]
    fn test_simd_mul_f32() {
        let a = [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0];
        let b = [2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0];
        let mut result = [0.0; 9];
        let expected = [2.0, 6.0, 12.0, 20.0, 30.0, 42.0, 56.0, 72.0, 90.0];

        SimdOps::mul_f32(&a, &b, &mut result);

        for (&res, &exp) in result.iter().zip(expected.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }
    }

    #[test]
    fn test_simd_add_scalar_f32() {
        let a = [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0];
        let scalar = 5.0;
        let mut result = [0.0; 11];
        let expected = [6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0];

        SimdOps::add_scalar_f32(&a, scalar, &mut result);

        for (&res, &exp) in result.iter().zip(expected.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }
    }

    #[test]
    fn test_simd_mul_scalar_f32() {
        let a = [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0];
        let scalar = 3.0;
        let mut result = [0.0; 8];
        let expected = [3.0, 6.0, 9.0, 12.0, 15.0, 18.0, 21.0, 24.0];

        SimdOps::mul_scalar_f32(&a, scalar, &mut result);

        for (&res, &exp) in result.iter().zip(expected.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }
    }

    #[test]
    fn test_simd_dot_product_f32() {
        let a = [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0];
        let b = [9.0, 8.0, 7.0, 6.0, 5.0, 4.0, 3.0, 2.0, 1.0];
        let expected = 165.0; // 1*9 + 2*8 + 3*7 + 4*6 + 5*5 + 6*4 + 7*3 + 8*2 + 9*1

        let result = SimdOps::dot_product_f32(&a, &b);

        assert_relative_eq!(result, expected, epsilon = 1e-6);
    }

    #[test]
    fn test_simd_sum_f32() {
        let a = [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0];
        let expected = 55.0; // Sum of 1 to 10

        let result = SimdOps::sum_f32(&a);

        assert_relative_eq!(result, expected, epsilon = 1e-6);
    }

    #[test]
    fn test_simd_exp_f32() {
        let a = [0.0, 1.0, 2.0, -1.0, 0.5];
        let mut result = [0.0; 5];
        let expected: Vec<f32> = a.iter().map(|x| x.exp()).collect();

        SimdOps::exp_f32(&a, &mut result);

        for (&res, &exp) in result.iter().zip(expected.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }
    }

    #[test]
    fn test_simd_sin_f32() {
        let a = [0.0, std::f32::consts::PI / 2.0, std::f32::consts::PI, -std::f32::consts::PI / 2.0];
        let mut result = [0.0; 4];
        let expected: Vec<f32> = a.iter().map(|x| x.sin()).collect();

        SimdOps::sin_f32(&a, &mut result);

        for (&res, &exp) in result.iter().zip(expected.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }
    }

    #[test]
    fn test_simd_cos_f32() {
        let a = [0.0, std::f32::consts::PI / 2.0, std::f32::consts::PI, -std::f32::consts::PI / 2.0];
        let mut result = [0.0; 4];
        let expected: Vec<f32> = a.iter().map(|x| x.cos()).collect();

        SimdOps::cos_f32(&a, &mut result);

        for (&res, &exp) in result.iter().zip(expected.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }
    }

    #[test]
    fn test_simd_trait_implementation() {
        let a = [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0];
        let b = [8.0, 7.0, 6.0, 5.0, 4.0, 3.0, 2.0, 1.0];
        let mut result = [0.0; 8];

        // Test trait methods
        a.simd_add(&b, &mut result);
        let expected_add = [9.0, 9.0, 9.0, 9.0, 9.0, 9.0, 9.0, 9.0];
        for (&res, &exp) in result.iter().zip(expected_add.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }

        let dot_result = a.simd_dot_product(&b);
        assert_relative_eq!(dot_result, 120.0, epsilon = 1e-6); // 1*8 + 2*7 + ... + 8*1

        let sum_result = a.simd_sum();
        assert_relative_eq!(sum_result, 36.0, epsilon = 1e-6); // Sum of 1 to 8
    }

    #[test]
    fn test_edge_cases() {
        // Test empty arrays
        let empty: [f32; 0] = [];
        let mut empty_result: [f32; 0] = [];
        SimdOps::add_f32(&empty, &empty, &mut empty_result);
        assert_eq!(SimdOps::sum_f32(&empty), 0.0);
        assert_eq!(SimdOps::dot_product_f32(&empty, &empty), 0.0);

        // Test single element
        let single = [42.0];
        let mut single_result = [0.0];
        SimdOps::add_scalar_f32(&single, 8.0, &mut single_result);
        assert_relative_eq!(single_result[0], 50.0, epsilon = 1e-6);

        // Test odd number of elements (not divisible by SIMD width)
        let odd = [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0];
        let sum = SimdOps::sum_f32(&odd);
        assert_relative_eq!(sum, 28.0, epsilon = 1e-6);
    }
}
