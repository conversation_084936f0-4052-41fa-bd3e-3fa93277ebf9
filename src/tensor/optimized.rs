//! Optimized tensor operations combining SIMD and parallel computing.
//!
//! This module provides a unified interface for high-performance tensor operations
//! that automatically choose between SIMD, parallel, or sequential implementations
//! based on data size and system capabilities. The optimization strategy is selected
//! dynamically to maximize performance for each specific operation.
//!
//! # Optimization Strategies
//!
//! The module supports four optimization strategies:
//!
//! 1. **Sequential**: Traditional single-threaded, scalar operations
//!    - Used for small tensors where overhead would reduce performance
//!    - Provides predictable, consistent performance
//!
//! 2. **SIMD**: Single Instruction, Multiple Data operations
//!    - Processes multiple elements simultaneously using vector instructions
//!    - Optimal for medium-sized tensors (64 - 10,000 elements)
//!    - Provides 2-8x speedup for compatible operations
//!
//! 3. **Parallel**: Multi-threaded operations using work-stealing
//!    - Distributes work across multiple CPU cores
//!    - Optimal for large tensors (>10,000 elements)
//!    - Scales with available CPU cores
//!
//! 4. **SIMD + Parallel**: Combined approach for maximum performance
//!    - Uses SIMD within each parallel chunk
//!    - Optimal for very large tensors (>100,000 elements)
//!    - Provides best overall performance on multi-core systems
//!
//! # Performance Guidelines
//!
//! | Tensor Size | Recommended Strategy | Expected Speedup |
//! |-------------|---------------------|------------------|
//! | < 64        | Sequential          | 1x (baseline)    |
//! | 64 - 1K     | SIMD               | 2-4x             |
//! | 1K - 10K    | SIMD               | 3-6x             |
//! | 10K - 100K  | Parallel           | 2-8x (cores)     |
//! | > 100K      | SIMD + Parallel    | 4-32x (combined) |
//!
//! # Examples
//!
//! ```rust
//! use qilin_inference::tensor::optimized::{OptimizationConfig, OptimizedOps};
//! use qilin_inference::tensor::parallel::ParallelConfig;
//!
//! // Create optimized configuration
//! let parallel_config = ParallelConfig::new(10_000, None, 1_000);
//! let opt_config = OptimizationConfig::new(parallel_config, 64, true);
//!
//! // The system automatically selects the best strategy
//! let strategy = opt_config.optimization_strategy(50_000);
//! // For 50K elements, this would typically select Parallel strategy
//! ```

use crate::tensor::{Numeric, simd::SimdOps, parallel::{ParallelOps, ParallelConfig}};

#[cfg(feature = "simd")]
use crate::tensor::simd::SimdOptimized;
use crate::tensor::parallel::ParallelOptimized;

/// Configuration for optimized tensor operations.
///
/// This struct controls the automatic selection of optimization strategies
/// based on tensor size and system characteristics. It combines SIMD and
/// parallel computing configurations to provide optimal performance across
/// different workload sizes.
///
/// # Strategy Selection
///
/// The optimization strategy is selected based on the following decision tree:
///
/// ```text
/// size < simd_threshold
/// ├─ true  → Sequential
/// └─ false → size < parallel_threshold
///            ├─ true  → prefer_simd ? SIMD : Sequential
///            └─ false → Parallel (or SIMD+Parallel if both enabled)
/// ```
///
/// # Thread Safety
///
/// OptimizationConfig is thread-safe and can be shared across multiple threads.
/// The configuration is immutable once created.
#[derive(Debug, Clone)]
pub struct OptimizationConfig {
    /// Configuration for parallel operations.
    ///
    /// Controls when and how parallel execution is performed, including
    /// thread limits, chunk sizes, and parallelization thresholds.
    pub parallel_config: ParallelConfig,

    /// Minimum number of elements to enable SIMD operations.
    ///
    /// SIMD operations have some overhead for setup and data alignment.
    /// For very small tensors, this overhead can outweigh the benefits.
    ///
    /// **Recommended values:**
    /// - Conservative: 64-128 elements
    /// - Aggressive: 16-32 elements
    /// - Memory-bound ops: 128-256 elements
    pub simd_threshold: usize,

    /// Whether to prefer SIMD over sequential for medium-sized tensors.
    ///
    /// When tensor size is between `simd_threshold` and `parallel_threshold`,
    /// this flag determines whether to use SIMD or sequential execution.
    ///
    /// **Guidelines:**
    /// - `true`: Better for compute-intensive operations (sin, exp, sqrt)
    /// - `false`: Better for memory-bound operations (copy, simple arithmetic)
    pub prefer_simd: bool,
}

impl Default for OptimizationConfig {
    fn default() -> Self {
        Self {
            parallel_config: ParallelConfig::default(),
            simd_threshold: 64,  // Use SIMD for arrays >= 64 elements
            prefer_simd: true,   // Prefer SIMD for medium arrays
        }
    }
}

impl OptimizationConfig {
    /// Create a new optimization configuration.
    pub fn new(parallel_config: ParallelConfig, simd_threshold: usize, prefer_simd: bool) -> Self {
        Self {
            parallel_config,
            simd_threshold,
            prefer_simd,
        }
    }
    
    /// Determine the best optimization strategy for the given size.
    pub fn optimization_strategy(&self, size: usize) -> OptimizationStrategy {
        if size < self.simd_threshold {
            OptimizationStrategy::Sequential
        } else if size < self.parallel_config.parallel_threshold {
            if self.prefer_simd {
                OptimizationStrategy::Simd
            } else {
                OptimizationStrategy::Sequential
            }
        } else {
            // For very large arrays, we could potentially use both SIMD and parallel
            // For now, prefer parallel for simplicity
            OptimizationStrategy::Parallel
        }
    }
}

/// Strategy for optimizing tensor operations.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum OptimizationStrategy {
    /// Use sequential implementation.
    Sequential,
    /// Use SIMD optimization.
    Simd,
    /// Use parallel processing.
    Parallel,
    /// Use both SIMD and parallel (future enhancement).
    SimdParallel,
}

/// Optimized operations that automatically choose the best implementation.
pub struct OptimizedOps;

impl OptimizedOps {
    /// Optimized element-wise addition for f32 slices.
    pub fn add_f32_optimized(a: &[f32], b: &[f32], result: &mut [f32], config: &OptimizationConfig) {
        match config.optimization_strategy(a.len()) {
            OptimizationStrategy::Sequential => {
                for ((a_val, b_val), res) in a.iter().zip(b.iter()).zip(result.iter_mut()) {
                    *res = a_val + b_val;
                }
            }
            #[cfg(feature = "simd")]
            OptimizationStrategy::Simd => {
                SimdOps::add_f32(a, b, result);
            }
            #[cfg(not(feature = "simd"))]
            OptimizationStrategy::Simd => {
                // Fallback to sequential if SIMD not available
                for ((a_val, b_val), res) in a.iter().zip(b.iter()).zip(result.iter_mut()) {
                    *res = a_val + b_val;
                }
            }
            OptimizationStrategy::Parallel => {
                ParallelOps::add_f32_parallel(a, b, result, &config.parallel_config);
            }
            OptimizationStrategy::SimdParallel => {
                // Future enhancement: combine SIMD and parallel
                ParallelOps::add_f32_parallel(a, b, result, &config.parallel_config);
            }
        }
    }
    
    /// Optimized element-wise multiplication for f32 slices.
    pub fn mul_f32_optimized(a: &[f32], b: &[f32], result: &mut [f32], config: &OptimizationConfig) {
        match config.optimization_strategy(a.len()) {
            OptimizationStrategy::Sequential => {
                for ((a_val, b_val), res) in a.iter().zip(b.iter()).zip(result.iter_mut()) {
                    *res = a_val * b_val;
                }
            }
            #[cfg(feature = "simd")]
            OptimizationStrategy::Simd => {
                SimdOps::mul_f32(a, b, result);
            }
            #[cfg(not(feature = "simd"))]
            OptimizationStrategy::Simd => {
                for ((a_val, b_val), res) in a.iter().zip(b.iter()).zip(result.iter_mut()) {
                    *res = a_val * b_val;
                }
            }
            OptimizationStrategy::Parallel => {
                ParallelOps::mul_f32_parallel(a, b, result, &config.parallel_config);
            }
            OptimizationStrategy::SimdParallel => {
                ParallelOps::mul_f32_parallel(a, b, result, &config.parallel_config);
            }
        }
    }
    
    /// Optimized scalar addition for f32 slices.
    pub fn add_scalar_f32_optimized(a: &[f32], scalar: f32, result: &mut [f32], config: &OptimizationConfig) {
        match config.optimization_strategy(a.len()) {
            OptimizationStrategy::Sequential => {
                for (a_val, res) in a.iter().zip(result.iter_mut()) {
                    *res = a_val + scalar;
                }
            }
            #[cfg(feature = "simd")]
            OptimizationStrategy::Simd => {
                SimdOps::add_scalar_f32(a, scalar, result);
            }
            #[cfg(not(feature = "simd"))]
            OptimizationStrategy::Simd => {
                for (a_val, res) in a.iter().zip(result.iter_mut()) {
                    *res = a_val + scalar;
                }
            }
            OptimizationStrategy::Parallel => {
                ParallelOps::add_scalar_f32_parallel(a, scalar, result, &config.parallel_config);
            }
            OptimizationStrategy::SimdParallel => {
                ParallelOps::add_scalar_f32_parallel(a, scalar, result, &config.parallel_config);
            }
        }
    }
    
    /// Optimized scalar multiplication for f32 slices.
    pub fn mul_scalar_f32_optimized(a: &[f32], scalar: f32, result: &mut [f32], config: &OptimizationConfig) {
        match config.optimization_strategy(a.len()) {
            OptimizationStrategy::Sequential => {
                for (a_val, res) in a.iter().zip(result.iter_mut()) {
                    *res = a_val * scalar;
                }
            }
            #[cfg(feature = "simd")]
            OptimizationStrategy::Simd => {
                SimdOps::mul_scalar_f32(a, scalar, result);
            }
            #[cfg(not(feature = "simd"))]
            OptimizationStrategy::Simd => {
                for (a_val, res) in a.iter().zip(result.iter_mut()) {
                    *res = a_val * scalar;
                }
            }
            OptimizationStrategy::Parallel => {
                ParallelOps::mul_scalar_f32_parallel(a, scalar, result, &config.parallel_config);
            }
            OptimizationStrategy::SimdParallel => {
                ParallelOps::mul_scalar_f32_parallel(a, scalar, result, &config.parallel_config);
            }
        }
    }
    
    /// Optimized dot product for f32 slices.
    pub fn dot_product_f32_optimized(a: &[f32], b: &[f32], config: &OptimizationConfig) -> f32 {
        match config.optimization_strategy(a.len()) {
            OptimizationStrategy::Sequential => {
                a.iter().zip(b.iter()).map(|(a_val, b_val)| a_val * b_val).sum()
            }
            #[cfg(feature = "simd")]
            OptimizationStrategy::Simd => {
                SimdOps::dot_product_f32(a, b)
            }
            #[cfg(not(feature = "simd"))]
            OptimizationStrategy::Simd => {
                a.iter().zip(b.iter()).map(|(a_val, b_val)| a_val * b_val).sum()
            }
            OptimizationStrategy::Parallel => {
                ParallelOps::dot_product_f32_parallel(a, b, &config.parallel_config)
            }
            OptimizationStrategy::SimdParallel => {
                ParallelOps::dot_product_f32_parallel(a, b, &config.parallel_config)
            }
        }
    }
    
    /// Optimized sum reduction for f32 slices.
    pub fn sum_f32_optimized(a: &[f32], config: &OptimizationConfig) -> f32 {
        match config.optimization_strategy(a.len()) {
            OptimizationStrategy::Sequential => {
                a.iter().sum()
            }
            #[cfg(feature = "simd")]
            OptimizationStrategy::Simd => {
                SimdOps::sum_f32(a)
            }
            #[cfg(not(feature = "simd"))]
            OptimizationStrategy::Simd => {
                a.iter().sum()
            }
            OptimizationStrategy::Parallel => {
                ParallelOps::sum_f32_parallel(a, &config.parallel_config)
            }
            OptimizationStrategy::SimdParallel => {
                ParallelOps::sum_f32_parallel(a, &config.parallel_config)
            }
        }
    }
    
    /// Optimized exponential function for f32 slices.
    pub fn exp_f32_optimized(a: &[f32], result: &mut [f32], config: &OptimizationConfig) {
        match config.optimization_strategy(a.len()) {
            OptimizationStrategy::Sequential => {
                for (a_val, res) in a.iter().zip(result.iter_mut()) {
                    *res = a_val.exp();
                }
            }
            #[cfg(feature = "simd")]
            OptimizationStrategy::Simd => {
                SimdOps::exp_f32(a, result);
            }
            #[cfg(not(feature = "simd"))]
            OptimizationStrategy::Simd => {
                for (a_val, res) in a.iter().zip(result.iter_mut()) {
                    *res = a_val.exp();
                }
            }
            OptimizationStrategy::Parallel => {
                ParallelOps::exp_f32_parallel(a, result, &config.parallel_config);
            }
            OptimizationStrategy::SimdParallel => {
                ParallelOps::exp_f32_parallel(a, result, &config.parallel_config);
            }
        }
    }
}

/// Trait for optimized operations on numeric types.
pub trait Optimized<T: Numeric> {
    /// Optimized element-wise addition.
    fn optimized_add(&self, other: &[T], result: &mut [T], config: &OptimizationConfig);
    
    /// Optimized element-wise multiplication.
    fn optimized_mul(&self, other: &[T], result: &mut [T], config: &OptimizationConfig);
    
    /// Optimized scalar addition.
    fn optimized_add_scalar(&self, scalar: T, result: &mut [T], config: &OptimizationConfig);
    
    /// Optimized scalar multiplication.
    fn optimized_mul_scalar(&self, scalar: T, result: &mut [T], config: &OptimizationConfig);
    
    /// Optimized dot product.
    fn optimized_dot_product(&self, other: &[T], config: &OptimizationConfig) -> T;
    
    /// Optimized sum reduction.
    fn optimized_sum(&self, config: &OptimizationConfig) -> T;
}

impl Optimized<f32> for [f32] {
    fn optimized_add(&self, other: &[f32], result: &mut [f32], config: &OptimizationConfig) {
        OptimizedOps::add_f32_optimized(self, other, result, config);
    }
    
    fn optimized_mul(&self, other: &[f32], result: &mut [f32], config: &OptimizationConfig) {
        OptimizedOps::mul_f32_optimized(self, other, result, config);
    }
    
    fn optimized_add_scalar(&self, scalar: f32, result: &mut [f32], config: &OptimizationConfig) {
        OptimizedOps::add_scalar_f32_optimized(self, scalar, result, config);
    }
    
    fn optimized_mul_scalar(&self, scalar: f32, result: &mut [f32], config: &OptimizationConfig) {
        OptimizedOps::mul_scalar_f32_optimized(self, scalar, result, config);
    }
    
    fn optimized_dot_product(&self, other: &[f32], config: &OptimizationConfig) -> f32 {
        OptimizedOps::dot_product_f32_optimized(self, other, config)
    }
    
    fn optimized_sum(&self, config: &OptimizationConfig) -> f32 {
        OptimizedOps::sum_f32_optimized(self, config)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use approx::assert_relative_eq;

    #[test]
    fn test_optimization_config() {
        let config = OptimizationConfig::default();
        assert_eq!(config.simd_threshold, 64);
        assert_eq!(config.prefer_simd, true);

        // Test strategy selection
        assert_eq!(config.optimization_strategy(32), OptimizationStrategy::Sequential);
        assert_eq!(config.optimization_strategy(128), OptimizationStrategy::Simd);
        assert_eq!(config.optimization_strategy(50_000), OptimizationStrategy::Parallel);
    }

    #[test]
    fn test_optimized_add_f32_sequential() {
        let a = vec![1.0, 2.0, 3.0, 4.0];
        let b = vec![5.0, 6.0, 7.0, 8.0];
        let mut result = vec![0.0; 4];
        let expected = vec![6.0, 8.0, 10.0, 12.0];

        let config = OptimizationConfig::new(
            ParallelConfig::default(),
            100, // High SIMD threshold to force sequential
            true
        );

        OptimizedOps::add_f32_optimized(&a, &b, &mut result, &config);

        for (&res, &exp) in result.iter().zip(expected.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }
    }

    #[test]
    fn test_optimized_add_f32_simd() {
        let a = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0];
        let b = vec![8.0, 7.0, 6.0, 5.0, 4.0, 3.0, 2.0, 1.0];
        let mut result = vec![0.0; 8];
        let expected = vec![9.0, 9.0, 9.0, 9.0, 9.0, 9.0, 9.0, 9.0];

        let config = OptimizationConfig::new(
            ParallelConfig::new(50_000, None, 1000), // High parallel threshold
            4, // Low SIMD threshold to force SIMD
            true
        );

        OptimizedOps::add_f32_optimized(&a, &b, &mut result, &config);

        for (&res, &exp) in result.iter().zip(expected.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }
    }

    #[test]
    fn test_optimized_add_f32_parallel() {
        let size = 20_000;
        let a: Vec<f32> = (0..size).map(|i| i as f32).collect();
        let b: Vec<f32> = (0..size).map(|i| (size - i) as f32).collect();
        let mut result = vec![0.0; size];

        let config = OptimizationConfig::new(
            ParallelConfig::new(10_000, None, 1000), // Low parallel threshold
            64,
            true
        );

        OptimizedOps::add_f32_optimized(&a, &b, &mut result, &config);

        // All results should be size (0+size, 1+(size-1), etc.)
        for &res in result.iter() {
            assert_relative_eq!(res, size as f32, epsilon = 1e-6);
        }
    }

    #[test]
    fn test_optimized_mul_f32() {
        let a = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0];
        let b = vec![2.0, 3.0, 4.0, 5.0, 6.0, 7.0];
        let mut result = vec![0.0; 6];
        let expected = vec![2.0, 6.0, 12.0, 20.0, 30.0, 42.0];

        let config = OptimizationConfig::new(
            ParallelConfig::default(),
            4, // Force SIMD for this size
            true
        );

        OptimizedOps::mul_f32_optimized(&a, &b, &mut result, &config);

        for (&res, &exp) in result.iter().zip(expected.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }
    }

    #[test]
    fn test_optimized_scalar_operations() {
        let a = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0];
        let mut add_result = vec![0.0; 8];
        let mut mul_result = vec![0.0; 8];

        let config = OptimizationConfig::new(
            ParallelConfig::default(),
            4, // Force SIMD
            true
        );

        OptimizedOps::add_scalar_f32_optimized(&a, 10.0, &mut add_result, &config);
        OptimizedOps::mul_scalar_f32_optimized(&a, 3.0, &mut mul_result, &config);

        let expected_add = vec![11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0];
        let expected_mul = vec![3.0, 6.0, 9.0, 12.0, 15.0, 18.0, 21.0, 24.0];

        for (&res, &exp) in add_result.iter().zip(expected_add.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }

        for (&res, &exp) in mul_result.iter().zip(expected_mul.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }
    }

    #[test]
    fn test_optimized_dot_product() {
        let a = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0];
        let b = vec![6.0, 5.0, 4.0, 3.0, 2.0, 1.0];
        let expected = 56.0; // 1*6 + 2*5 + 3*4 + 4*3 + 5*2 + 6*1

        let config = OptimizationConfig::new(
            ParallelConfig::default(),
            4, // Force SIMD
            true
        );

        let result = OptimizedOps::dot_product_f32_optimized(&a, &b, &config);
        assert_relative_eq!(result, expected, epsilon = 1e-6);
    }

    #[test]
    fn test_optimized_sum() {
        let a = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0];
        let expected = 55.0; // Sum of 1 to 10

        let config = OptimizationConfig::new(
            ParallelConfig::default(),
            4, // Force SIMD
            true
        );

        let result = OptimizedOps::sum_f32_optimized(&a, &config);
        assert_relative_eq!(result, expected, epsilon = 1e-6);
    }

    #[test]
    fn test_optimized_exp() {
        let a = vec![0.0, 1.0, 2.0, -1.0, 0.5];
        let mut result = vec![0.0; 5];
        let expected: Vec<f32> = a.iter().map(|x| x.exp()).collect();

        let config = OptimizationConfig::new(
            ParallelConfig::default(),
            3, // Force SIMD
            true
        );

        OptimizedOps::exp_f32_optimized(&a, &mut result, &config);

        for (&res, &exp) in result.iter().zip(expected.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }
    }

    #[test]
    fn test_optimized_trait_implementation() {
        let a = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0];
        let b = vec![8.0, 7.0, 6.0, 5.0, 4.0, 3.0, 2.0, 1.0];
        let mut result = vec![0.0; 8];

        let config = OptimizationConfig::new(
            ParallelConfig::default(),
            4, // Force SIMD
            true
        );

        // Test trait methods
        a.optimized_add(&b, &mut result, &config);
        let expected_add = vec![9.0, 9.0, 9.0, 9.0, 9.0, 9.0, 9.0, 9.0];
        for (&res, &exp) in result.iter().zip(expected_add.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }

        let dot_result = a.optimized_dot_product(&b, &config);
        assert_relative_eq!(dot_result, 120.0, epsilon = 1e-6); // 1*8 + 2*7 + ... + 8*1

        let sum_result = a.optimized_sum(&config);
        assert_relative_eq!(sum_result, 36.0, epsilon = 1e-6); // Sum of 1 to 8
    }

    #[test]
    fn test_strategy_selection_edge_cases() {
        let config = OptimizationConfig::new(
            ParallelConfig::new(1000, None, 100),
            50,
            false // Don't prefer SIMD
        );

        // Below SIMD threshold
        assert_eq!(config.optimization_strategy(30), OptimizationStrategy::Sequential);

        // Between SIMD and parallel threshold, but prefer_simd = false
        assert_eq!(config.optimization_strategy(500), OptimizationStrategy::Sequential);

        // Above parallel threshold
        assert_eq!(config.optimization_strategy(2000), OptimizationStrategy::Parallel);

        // Test with prefer_simd = true
        let config_simd = OptimizationConfig::new(
            ParallelConfig::new(1000, None, 100),
            50,
            true // Prefer SIMD
        );

        assert_eq!(config_simd.optimization_strategy(500), OptimizationStrategy::Simd);
    }
}
