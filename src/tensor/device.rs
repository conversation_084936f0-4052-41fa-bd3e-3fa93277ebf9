//! Device abstraction for tensor operations.

use crate::error::TensorError;

/// Device types supported by the inference engine.
#[derive(Debug, <PERSON>lone, PartialEq, Eq)]
pub enum DeviceType {
    /// CPU device with optional thread count.
    Cpu { num_threads: Option<usize> },
    /// CUDA GPU device.
    #[cfg(feature = "cuda")]
    Cuda { device_id: usize },
    /// Metal GPU device (Apple Silicon).
    #[cfg(feature = "metal")]
    Metal { device_id: usize },
}

/// Memory information for a device.
#[derive(Debug, <PERSON>lone)]
pub struct MemoryInfo {
    /// Total memory available on the device (in bytes).
    pub total_memory: usize,
    /// Currently used memory (in bytes).
    pub used_memory: usize,
    /// Available memory (in bytes).
    pub available_memory: usize,
}

/// Compute capability information for a device.
#[derive(Debug, Clone)]
pub struct ComputeCapability {
    /// Maximum number of threads per block.
    pub max_threads_per_block: usize,
    /// Maximum block dimensions.
    pub max_block_dims: [usize; 3],
    /// Maximum grid dimensions.
    pub max_grid_dims: [usize; 3],
    /// Shared memory per block (in bytes).
    pub shared_memory_per_block: usize,
    /// Warp size.
    pub warp_size: usize,
}

/// Device configuration.
#[derive(Debug, Clone)]
pub struct DeviceConfig {
    /// Device type.
    pub device_type: DeviceType,
    /// Memory pool size (in bytes).
    pub memory_pool_size: Option<usize>,
    /// Enable memory mapping.
    pub enable_memory_mapping: bool,
    /// Enable unified memory (CUDA).
    pub enable_unified_memory: bool,
}

/// Information about a device.
#[derive(Debug, Clone)]
pub struct DeviceInfo {
    /// Device name.
    pub name: String,
    /// Device type.
    pub device_type: DeviceType,
    /// Memory information.
    pub memory_info: MemoryInfo,
    /// Compute capability.
    pub compute_capability: ComputeCapability,
}

/// Trait for device abstraction.
pub trait Device: Send + Sync {
    /// Error type for device operations.
    type Error: std::error::Error + Send + Sync + 'static;
    
    /// Get the device type.
    fn device_type(&self) -> DeviceType;
    
    /// Get memory information.
    fn memory_info(&self) -> MemoryInfo;
    
    /// Get compute capability.
    fn compute_capability(&self) -> ComputeCapability;
    
    /// Synchronize device operations.
    fn synchronize(&self) -> Result<(), Self::Error>;
    
    /// Clear device cache.
    fn clear_cache(&self) -> Result<(), Self::Error>;
}

/// Trait for device factory.
pub trait DeviceFactory {
    /// Device type created by this factory.
    type Device: Device;
    /// Error type for device creation.
    type Error: std::error::Error + Send + Sync + 'static;
    
    /// Create a device with the given configuration.
    fn create_device(&self, config: &DeviceConfig) -> Result<Self::Device, Self::Error>;
    
    /// List available devices.
    fn list_available_devices(&self) -> Vec<DeviceInfo>;
}

/// CPU device implementation.
#[derive(Debug)]
pub struct CpuDevice {
    num_threads: usize,
}

impl CpuDevice {
    /// Create a new CPU device.
    pub fn new(num_threads: Option<usize>) -> Self {
        let num_threads = num_threads.unwrap_or_else(num_cpus::get);
        Self { num_threads }
    }
}

impl Device for CpuDevice {
    type Error = TensorError;
    
    fn device_type(&self) -> DeviceType {
        DeviceType::Cpu {
            num_threads: Some(self.num_threads),
        }
    }
    
    fn memory_info(&self) -> MemoryInfo {
        // Get system memory information
        // This is a simplified implementation
        MemoryInfo {
            total_memory: 8 * 1024 * 1024 * 1024, // 8GB placeholder
            used_memory: 0,
            available_memory: 8 * 1024 * 1024 * 1024,
        }
    }
    
    fn compute_capability(&self) -> ComputeCapability {
        ComputeCapability {
            max_threads_per_block: self.num_threads,
            max_block_dims: [self.num_threads, 1, 1],
            max_grid_dims: [1, 1, 1],
            shared_memory_per_block: 0,
            warp_size: 1,
        }
    }
    
    fn synchronize(&self) -> Result<(), Self::Error> {
        // CPU operations are synchronous by default
        Ok(())
    }
    
    fn clear_cache(&self) -> Result<(), Self::Error> {
        // No cache to clear on CPU
        Ok(())
    }
}

/// CPU device factory.
pub struct CpuDeviceFactory;

impl DeviceFactory for CpuDeviceFactory {
    type Device = CpuDevice;
    type Error = TensorError;
    
    fn create_device(&self, config: &DeviceConfig) -> Result<Self::Device, Self::Error> {
        match &config.device_type {
            DeviceType::Cpu { num_threads } => Ok(CpuDevice::new(*num_threads)),
            #[cfg(feature = "cuda")]
            DeviceType::Cuda { .. } => Err(TensorError::DeviceMismatch {
                tensor_device: "cpu_factory".to_string(),
                required_device: "cuda".to_string(),
                context: Some(crate::error::ErrorContext::new("create_device", "tensor::device")),
            }),
            #[cfg(feature = "metal")]
            DeviceType::Metal { .. } => Err(TensorError::DeviceMismatch {
                tensor_device: "cpu_factory".to_string(),
                required_device: "metal".to_string(),
                context: Some(crate::error::ErrorContext::new("create_device", "tensor::device")),
            }),
        }
    }
    
    fn list_available_devices(&self) -> Vec<DeviceInfo> {
        vec![DeviceInfo {
            name: "CPU".to_string(),
            device_type: DeviceType::Cpu {
                num_threads: Some(num_cpus::get()),
            },
            memory_info: MemoryInfo {
                total_memory: 8 * 1024 * 1024 * 1024, // 8GB placeholder
                used_memory: 0,
                available_memory: 8 * 1024 * 1024 * 1024,
            },
            compute_capability: ComputeCapability {
                max_threads_per_block: num_cpus::get(),
                max_block_dims: [num_cpus::get(), 1, 1],
                max_grid_dims: [1, 1, 1],
                shared_memory_per_block: 0,
                warp_size: 1,
            },
        }]
    }
}

/// Get the default device.
pub fn default_device() -> CpuDevice {
    CpuDevice::new(None)
}

/// Get the number of available CPU cores.
pub fn num_cpus() -> usize {
    num_cpus::get()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cpu_device() {
        let device = CpuDevice::new(Some(4));
        assert_eq!(device.device_type(), DeviceType::Cpu { num_threads: Some(4) });
        assert!(device.synchronize().is_ok());
        assert!(device.clear_cache().is_ok());
    }
    
    #[test]
    fn test_cpu_device_factory() {
        let factory = CpuDeviceFactory;
        let config = DeviceConfig {
            device_type: DeviceType::Cpu { num_threads: Some(2) },
            memory_pool_size: None,
            enable_memory_mapping: false,
            enable_unified_memory: false,
        };
        
        let device = factory.create_device(&config).unwrap();
        assert_eq!(device.device_type(), DeviceType::Cpu { num_threads: Some(2) });
    }
}
