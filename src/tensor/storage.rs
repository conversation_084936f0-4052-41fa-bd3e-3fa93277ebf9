//! Tensor storage abstractions.

use crate::tensor::Numeric;

/// Trait for tensor storage backends.
pub trait Storage<T: Numeric>: Send + Sync {
    /// Get a slice of the stored data.
    fn as_slice(&self) -> &[T];
    
    /// Get the length of the storage.
    fn len(&self) -> usize;
    
    /// Check if the storage is empty.
    fn is_empty(&self) -> bool {
        self.len() == 0
    }
    
    /// Check if the storage is contiguous in memory.
    fn is_contiguous(&self) -> bool;
}

/// Trait for mutable tensor storage.
pub trait StorageMut<T: Numeric>: Storage<T> {
    /// Get a mutable slice of the stored data.
    fn as_mut_slice(&mut self) -> &mut [T];
}
