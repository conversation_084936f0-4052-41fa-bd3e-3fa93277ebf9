//! In-place tensor operations for memory-efficient computations.
//!
//! This module provides traits and implementations for in-place tensor operations
//! that modify tensors without allocating new memory. This is crucial for:
//! - Memory efficiency in large-scale computations
//! - Performance optimization by avoiding unnecessary allocations
//! - Gradient computation in neural networks
//! - Real-time inference scenarios

use crate::tensor::Numeric;
use std::error::Error;

/// Trait for in-place tensor operations.
/// 
/// In-place operations modify the tensor's data directly without creating new tensors,
/// providing significant memory and performance benefits for large computations.
/// 
/// # Safety
/// In-place operations modify the original tensor data. Ensure that:
/// - No other references to the tensor data exist during modification
/// - The tensor owns its data (not a view or borrowed slice)
/// - Thread safety is maintained in concurrent environments
/// 
/// # Examples
/// ```rust,ignore
/// use qilin_inference::tensor::{from_slice, Shape, InplaceOps};
/// 
/// let mut tensor = from_slice(&[1.0, 2.0, 3.0, 4.0], &Shape::new(vec![2, 2]))?;
/// 
/// // In-place sine operation
/// tensor.sin_inplace()?;
/// 
/// // In-place scalar addition
/// tensor.add_scalar_inplace(1.0)?;
/// 
/// // In-place element-wise multiplication with another tensor
/// let other = from_slice(&[2.0, 2.0, 2.0, 2.0], &Shape::new(vec![2, 2]))?;
/// tensor.mul_inplace(&other)?;
/// ```
pub trait InplaceOps<T: Numeric> {
    /// Error type for in-place operations.
    type Error: Error + Send + Sync + 'static;

    // ========== Mathematical Functions (In-place) ==========
    
    /// Apply sine function in-place.
    fn sin_inplace(&mut self) -> Result<(), Self::Error>;
    
    /// Apply cosine function in-place.
    fn cos_inplace(&mut self) -> Result<(), Self::Error>;
    
    /// Apply tangent function in-place.
    fn tan_inplace(&mut self) -> Result<(), Self::Error>;
    
    /// Apply arcsine function in-place.
    fn asin_inplace(&mut self) -> Result<(), Self::Error>;
    
    /// Apply arccosine function in-place.
    fn acos_inplace(&mut self) -> Result<(), Self::Error>;
    
    /// Apply arctangent function in-place.
    fn atan_inplace(&mut self) -> Result<(), Self::Error>;
    
    /// Apply exponential function in-place.
    fn exp_inplace(&mut self) -> Result<(), Self::Error>;
    
    /// Apply natural logarithm in-place.
    fn log_inplace(&mut self) -> Result<(), Self::Error>;
    
    /// Apply base-2 logarithm in-place.
    fn log2_inplace(&mut self) -> Result<(), Self::Error>;
    
    /// Apply base-10 logarithm in-place.
    fn log10_inplace(&mut self) -> Result<(), Self::Error>;
    
    /// Apply square root in-place.
    fn sqrt_inplace(&mut self) -> Result<(), Self::Error>;
    
    /// Apply power function with scalar exponent in-place.
    fn pow_scalar_inplace(&mut self, exp: T) -> Result<(), Self::Error>;
    
    /// Apply hyperbolic sine in-place.
    fn sinh_inplace(&mut self) -> Result<(), Self::Error>;
    
    /// Apply hyperbolic cosine in-place.
    fn cosh_inplace(&mut self) -> Result<(), Self::Error>;
    
    /// Apply hyperbolic tangent in-place.
    fn tanh_inplace(&mut self) -> Result<(), Self::Error>;
    
    /// Apply floor function in-place.
    fn floor_inplace(&mut self) -> Result<(), Self::Error>;
    
    /// Apply ceiling function in-place.
    fn ceil_inplace(&mut self) -> Result<(), Self::Error>;
    
    /// Apply round function in-place.
    fn round_inplace(&mut self) -> Result<(), Self::Error>;
    
    /// Apply truncate function in-place.
    fn trunc_inplace(&mut self) -> Result<(), Self::Error>;
    
    /// Apply absolute value in-place.
    fn abs_inplace(&mut self) -> Result<(), Self::Error>;
    
    /// Clamp values between min and max in-place.
    fn clamp_inplace(&mut self, min: T, max: T) -> Result<(), Self::Error>;

    // ========== Binary Operations (In-place) ==========
    
    /// Add another tensor element-wise in-place.
    /// 
    /// # Arguments
    /// * `other` - The tensor to add to this tensor
    /// 
    /// # Errors
    /// Returns error if shapes are not compatible for broadcasting.
    fn add_inplace(&mut self, other: &Self) -> Result<(), Self::Error>;
    
    /// Subtract another tensor element-wise in-place.
    fn sub_inplace(&mut self, other: &Self) -> Result<(), Self::Error>;
    
    /// Multiply by another tensor element-wise in-place.
    fn mul_inplace(&mut self, other: &Self) -> Result<(), Self::Error>;
    
    /// Divide by another tensor element-wise in-place.
    fn div_inplace(&mut self, other: &Self) -> Result<(), Self::Error>;
    
    /// Element-wise power with another tensor in-place.
    fn pow_inplace(&mut self, other: &Self) -> Result<(), Self::Error>;

    // ========== Scalar Operations (In-place) ==========
    
    /// Add a scalar value to all elements in-place.
    fn add_scalar_inplace(&mut self, scalar: T) -> Result<(), Self::Error>;
    
    /// Subtract a scalar value from all elements in-place.
    fn sub_scalar_inplace(&mut self, scalar: T) -> Result<(), Self::Error>;
    
    /// Multiply all elements by a scalar in-place.
    fn mul_scalar_inplace(&mut self, scalar: T) -> Result<(), Self::Error>;
    
    /// Divide all elements by a scalar in-place.
    fn div_scalar_inplace(&mut self, scalar: T) -> Result<(), Self::Error>;

    // ========== Activation Functions (In-place) ==========
    
    /// Apply ReLU activation in-place: max(0, x).
    fn relu_inplace(&mut self) -> Result<(), Self::Error>;
    
    /// Apply Leaky ReLU activation in-place: max(alpha * x, x).
    fn leaky_relu_inplace(&mut self, alpha: T) -> Result<(), Self::Error>;
    
    /// Apply ELU activation in-place: x if x > 0, alpha * (exp(x) - 1) otherwise.
    fn elu_inplace(&mut self, alpha: T) -> Result<(), Self::Error>;
    
    /// Apply GELU activation in-place.
    fn gelu_inplace(&mut self) -> Result<(), Self::Error>;
    
    /// Apply sigmoid activation in-place: 1 / (1 + exp(-x)).
    fn sigmoid_inplace(&mut self) -> Result<(), Self::Error>;
    
    /// Apply softmax along a dimension in-place.
    /// 
    /// # Arguments
    /// * `dim` - The dimension along which to apply softmax
    fn softmax_inplace(&mut self, dim: usize) -> Result<(), Self::Error>;
    
    /// Apply log softmax along a dimension in-place.
    fn log_softmax_inplace(&mut self, dim: usize) -> Result<(), Self::Error>;

    // ========== Utility Operations (In-place) ==========
    
    /// Fill the tensor with a constant value.
    fn fill_(&mut self, value: T) -> Result<(), Self::Error>;
    
    /// Fill the tensor with zeros.
    fn zero_(&mut self) -> Result<(), Self::Error> {
        self.fill_(T::ZERO)
    }
    
    /// Fill the tensor with ones.
    fn one_(&mut self) -> Result<(), Self::Error> {
        self.fill_(T::ONE)
    }
    
    /// Copy data from another tensor in-place.
    /// 
    /// # Arguments
    /// * `other` - The tensor to copy from
    /// 
    /// # Errors
    /// Returns error if shapes don't match exactly.
    fn copy_(&mut self, other: &Self) -> Result<(), Self::Error>;
    
    /// Swap the contents of this tensor with another tensor.
    fn swap_(&mut self, other: &mut Self) -> Result<(), Self::Error>;

    // ========== Advanced In-place Operations ==========
    
    /// Apply a masked fill operation in-place.
    /// 
    /// # Arguments
    /// * `mask` - Boolean tensor indicating which elements to fill
    /// * `value` - The value to fill masked positions with
    fn masked_fill_inplace(&mut self, mask: &Self, value: T) -> Result<(), Self::Error>;
    
    /// Apply dropout in-place during training.
    /// 
    /// # Arguments
    /// * `p` - Dropout probability (0.0 to 1.0)
    /// * `training` - Whether in training mode
    fn dropout_inplace(&mut self, p: f32, training: bool) -> Result<(), Self::Error>;
    
    /// Normalize the tensor in-place along a dimension.
    /// 
    /// # Arguments
    /// * `dim` - The dimension along which to normalize
    /// * `eps` - Small value to avoid division by zero
    fn normalize_inplace(&mut self, dim: usize, eps: T) -> Result<(), Self::Error>;
}

/// Trait for creating in-place views of tensors.
/// 
/// This trait allows creating mutable views of tensor data for in-place operations
/// without transferring ownership.
pub trait InplaceView<T: Numeric> {
    /// The view type that implements InplaceOps.
    type View: InplaceOps<T>;
    
    /// Create a mutable view of this tensor for in-place operations.
    /// 
    /// # Safety
    /// The caller must ensure that no other references to the tensor data exist
    /// during the lifetime of the returned view.
    fn view_mut(&mut self) -> Self::View;
    
    /// Create a mutable view of a slice of this tensor.
    /// 
    /// # Arguments
    /// * `ranges` - The ranges to slice along each dimension
    fn slice_mut(&mut self, ranges: &[std::ops::Range<usize>]) -> Result<Self::View, Self::Error>;
    
    /// Error type for view operations.
    type Error: Error + Send + Sync + 'static;
}

/// Memory-efficient operation chaining for in-place operations.
/// 
/// This trait allows chaining multiple in-place operations efficiently.
pub trait InplaceChain<T: Numeric>: InplaceOps<T> {
    /// Chain multiple in-place operations.
    /// 
    /// # Examples
    /// ```rust,ignore
    /// tensor.chain_inplace(|t| {
    ///     t.sin_inplace()?;
    ///     t.add_scalar_inplace(1.0)?;
    ///     t.mul_scalar_inplace(2.0)?;
    ///     Ok(())
    /// })?;
    /// ```
    fn chain_inplace<F>(&mut self, operations: F) -> Result<(), Self::Error>
    where
        F: FnOnce(&mut Self) -> Result<(), Self::Error>;
}

// Default implementation for InplaceChain
impl<T: Numeric, U: InplaceOps<T>> InplaceChain<T> for U {
    fn chain_inplace<F>(&mut self, operations: F) -> Result<(), Self::Error>
    where
        F: FnOnce(&mut Self) -> Result<(), Self::Error>
    {
        operations(self)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    // Tests will be added when implementing the trait for CpuTensor
}
