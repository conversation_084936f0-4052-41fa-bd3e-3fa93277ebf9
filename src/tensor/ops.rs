//! Tensor operations implementation.

use crate::tensor::{Tensor, TensorOps, Numeric};
use crate::tensor::cpu::CpuTensor;
use crate::error::{TensorError, ErrorContext};

impl<T: Numeric> TensorOps<T> for CpuTensor<T> {
    fn add(&self, other: &Self) -> Result<Self, Self::Error> {
        // Check shape compatibility
        let result_shape = self.shape().broadcast_with(other.shape())?;
        
        // For now, implement element-wise addition for same-shaped tensors
        if self.shape() != other.shape() {
            return Err(TensorError::ShapeMismatch {
                expected: self.shape().dims().to_vec(),
                actual: other.shape().dims().to_vec(),
                context: Some(ErrorContext::new("add", "tensor::ops")),
            });
        }
        
        let self_data = self.data();
        let other_data = other.data();
        let result_data: Vec<T> = self_data.iter()
            .zip(other_data.iter())
            .map(|(&a, &b)| a + b)
            .collect();
        
        CpuTensor::from_data(result_data, result_shape)
    }
    
    fn sub(&self, other: &Self) -> Result<Self, Self::Error> {
        if self.shape() != other.shape() {
            return Err(TensorError::ShapeMismatch {
                expected: self.shape().dims().to_vec(),
                actual: other.shape().dims().to_vec(),
                context: Some(ErrorContext::new("sub", "tensor::ops")),
            });
        }
        
        let self_data = self.data();
        let other_data = other.data();
        let result_data: Vec<T> = self_data.iter()
            .zip(other_data.iter())
            .map(|(&a, &b)| a - b)
            .collect();
        
        CpuTensor::from_data(result_data, self.shape().clone())
    }
    
    fn mul(&self, other: &Self) -> Result<Self, Self::Error> {
        if self.shape() != other.shape() {
            return Err(TensorError::ShapeMismatch {
                expected: self.shape().dims().to_vec(),
                actual: other.shape().dims().to_vec(),
                context: Some(ErrorContext::new("mul", "tensor::ops")),
            });
        }
        
        let self_data = self.data();
        let other_data = other.data();
        let result_data: Vec<T> = self_data.iter()
            .zip(other_data.iter())
            .map(|(&a, &b)| a * b)
            .collect();
        
        CpuTensor::from_data(result_data, self.shape().clone())
    }
    
    fn div(&self, other: &Self) -> Result<Self, Self::Error> {
        if self.shape() != other.shape() {
            return Err(TensorError::ShapeMismatch {
                expected: self.shape().dims().to_vec(),
                actual: other.shape().dims().to_vec(),
                context: Some(ErrorContext::new("div", "tensor::ops")),
            });
        }
        
        let self_data = self.data();
        let other_data = other.data();
        let result_data: Vec<T> = self_data.iter()
            .zip(other_data.iter())
            .map(|(&a, &b)| a / b)
            .collect();
        
        CpuTensor::from_data(result_data, self.shape().clone())
    }
    
    fn add_scalar(&self, scalar: T) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x + scalar).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }
    
    fn sub_scalar(&self, scalar: T) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x - scalar).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }
    
    fn mul_scalar(&self, scalar: T) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x * scalar).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }
    
    fn div_scalar(&self, scalar: T) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x / scalar).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }
    
    fn matmul(&self, other: &Self) -> Result<Self, Self::Error> {
        // Simplified matrix multiplication for 2D tensors
        if self.shape().rank() != 2 || other.shape().rank() != 2 {
            return Err(TensorError::InvalidDimension {
                dimension: self.shape().rank().min(other.shape().rank()),
                total_dims: 2,
                context: Some(ErrorContext::new("matmul", "tensor::ops")),
            });
        }
        
        let [m, k] = [self.shape().dims()[0], self.shape().dims()[1]];
        let [k2, n] = [other.shape().dims()[0], other.shape().dims()[1]];
        
        if k != k2 {
            return Err(TensorError::ShapeMismatch {
                expected: vec![k],
                actual: vec![k2],
                context: Some(ErrorContext::new("matmul", "tensor::ops")),
            });
        }
        
        let mut result = vec![T::ZERO; m * n];
        let a_data = self.data();
        let b_data = other.data();
        
        for i in 0..m {
            for j in 0..n {
                let mut sum = T::ZERO;
                for k_idx in 0..k {
                    let a_val = a_data[i * k + k_idx];
                    let b_val = b_data[k_idx * n + j];
                    sum = sum + a_val * b_val;
                }
                result[i * n + j] = sum;
            }
        }
        
        let result_shape = crate::tensor::Shape::new(vec![m, n]);
        CpuTensor::from_data(result, result_shape)
    }
    
    fn dot(&self, other: &Self) -> Result<T, Self::Error> {
        if self.shape() != other.shape() {
            return Err(TensorError::ShapeMismatch {
                expected: self.shape().dims().to_vec(),
                actual: other.shape().dims().to_vec(),
                context: Some(ErrorContext::new("dot", "tensor::ops")),
            });
        }
        
        let self_data = self.data();
        let other_data = other.data();
        let result = self_data.iter()
            .zip(other_data.iter())
            .map(|(&a, &b)| a * b)
            .fold(T::ZERO, |acc, x| acc + x);
        
        Ok(result)
    }
    
    fn sum(&self, dim: Option<usize>, keepdim: bool) -> Result<Self, Self::Error> {
        match dim {
            None => {
                // Sum all elements
                let data = self.data();
                let total = data.iter().fold(T::ZERO, |acc, &x| acc + x);
                let result_shape = if keepdim {
                    crate::tensor::Shape::new(vec![1; self.shape().rank()])
                } else {
                    crate::tensor::Shape::new(vec![])
                };
                CpuTensor::from_data(vec![total], result_shape)
            }
            Some(_dim) => {
                // TODO: Implement dimension-specific sum
                Err(TensorError::DataTypeIncompatible {
                    operation: "sum with dimension".to_string(),
                    dtype: "not implemented".to_string(),
                    context: Some(ErrorContext::new("sum", "tensor::ops")),
                })
            }
        }
    }
    
    fn mean(&self, dim: Option<usize>, keepdim: bool) -> Result<Self, Self::Error> {
        let sum_result = self.sum(dim, keepdim)?;
        let count = T::from_f32(self.size() as f32);
        sum_result.div_scalar(count)
    }
    
    fn max(&self, dim: Option<usize>, keepdim: bool) -> Result<Self, Self::Error> {
        match dim {
            None => {
                // Find maximum of all elements
                let data = self.data();
                if data.is_empty() {
                    return Err(TensorError::InvalidDimension {
                        dimension: 0,
                        total_dims: 1,
                        context: Some(ErrorContext::new("max", "tensor::ops")),
                    });
                }

                let max_val = data.iter().fold(data[0], |acc, &x| acc.max(x));
                let result_shape = if keepdim {
                    crate::tensor::Shape::new(vec![1; self.shape().rank()])
                } else {
                    crate::tensor::Shape::new(vec![])
                };
                CpuTensor::from_data(vec![max_val], result_shape)
            }
            Some(_dim) => {
                // TODO: Implement dimension-specific max
                Err(TensorError::DataTypeIncompatible {
                    operation: "max with dimension".to_string(),
                    dtype: "not implemented".to_string(),
                    context: Some(ErrorContext::new("max", "tensor::ops")),
                })
            }
        }
    }

    fn min(&self, dim: Option<usize>, keepdim: bool) -> Result<Self, Self::Error> {
        match dim {
            None => {
                // Find minimum of all elements
                let data = self.data();
                if data.is_empty() {
                    return Err(TensorError::InvalidDimension {
                        dimension: 0,
                        total_dims: 1,
                        context: Some(ErrorContext::new("min", "tensor::ops")),
                    });
                }

                let min_val = data.iter().fold(data[0], |acc, &x| acc.min(x));
                let result_shape = if keepdim {
                    crate::tensor::Shape::new(vec![1; self.shape().rank()])
                } else {
                    crate::tensor::Shape::new(vec![])
                };
                CpuTensor::from_data(vec![min_val], result_shape)
            }
            Some(_dim) => {
                // TODO: Implement dimension-specific min
                Err(TensorError::DataTypeIncompatible {
                    operation: "min with dimension".to_string(),
                    dtype: "not implemented".to_string(),
                    context: Some(ErrorContext::new("min", "tensor::ops")),
                })
            }
        }
    }
    
    fn relu(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter()
            .map(|&x| x.max(T::ZERO))
            .collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }
    
    fn gelu(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter()
            .map(|&x| {
                // GELU(x) = 0.5 * x * (1 + tanh(sqrt(2/π) * (x + 0.044715 * x^3)))
                let x_f32 = x.to_f32();
                let sqrt_2_over_pi = (2.0 / std::f32::consts::PI).sqrt();
                let inner = sqrt_2_over_pi * (x_f32 + 0.044715 * x_f32.powi(3));
                let gelu_val = 0.5 * x_f32 * (1.0 + inner.tanh());
                T::from_f32(gelu_val)
            })
            .collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }
    
    fn softmax(&self, dim: usize) -> Result<Self, Self::Error> {
        if dim >= self.shape().rank() {
            return Err(TensorError::InvalidDimension {
                dimension: dim,
                total_dims: self.shape().rank(),
                context: Some(ErrorContext::new("softmax", "tensor::ops")),
            });
        }

        // For numerical stability, subtract max before exp
        let max_vals = self.max(Some(dim), true)?;
        let shifted = self.sub(&max_vals)?;
        let exp_vals = shifted.exp()?;
        let sum_exp = exp_vals.sum(Some(dim), true)?;
        exp_vals.div(&sum_exp)
    }

    fn log_softmax(&self, dim: usize) -> Result<Self, Self::Error> {
        if dim >= self.shape().rank() {
            return Err(TensorError::InvalidDimension {
                dimension: dim,
                total_dims: self.shape().rank(),
                context: Some(ErrorContext::new("log_softmax", "tensor::ops")),
            });
        }

        // For numerical stability: log_softmax(x) = x - log(sum(exp(x)))
        let max_vals = self.max(Some(dim), true)?;
        let shifted = self.sub(&max_vals)?;
        let exp_vals = shifted.exp()?;
        let sum_exp = exp_vals.sum(Some(dim), true)?;
        let log_sum_exp = sum_exp.log()?;
        self.sub(&max_vals)?.sub(&log_sum_exp)
    }
    
    fn tanh(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x.tanh()).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }
    
    fn sigmoid(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter()
            .map(|&x| {
                let x_f32 = x.to_f32();
                let sigmoid_val = 1.0 / (1.0 + (-x_f32).exp());
                T::from_f32(sigmoid_val)
            })
            .collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }

    // Mathematical functions
    fn sin(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x.sin()).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }

    fn cos(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x.cos()).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }

    fn tan(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x.tan()).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }

    fn asin(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x.asin()).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }

    fn acos(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x.acos()).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }

    fn atan(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x.atan()).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }

    fn exp(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x.exp()).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }

    fn log(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x.ln()).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }

    fn log2(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x.log2()).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }

    fn log10(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x.log10()).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }

    fn sqrt(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x.sqrt()).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }

    fn pow(&self, exp: &Self) -> Result<Self, Self::Error> {
        if self.shape() != exp.shape() {
            return Err(TensorError::ShapeMismatch {
                expected: self.shape().dims().to_vec(),
                actual: exp.shape().dims().to_vec(),
                context: Some(ErrorContext::new("pow", "tensor::ops")),
            });
        }

        let self_data = self.data();
        let exp_data = exp.data();
        let result_data: Vec<T> = self_data.iter()
            .zip(exp_data.iter())
            .map(|(&base, &exp)| base.pow(exp))
            .collect();

        CpuTensor::from_data(result_data, self.shape().clone())
    }

    fn pow_scalar(&self, exp: T) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x.pow(exp)).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }

    fn sinh(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x.sinh()).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }

    fn cosh(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x.cosh()).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }

    fn floor(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x.floor()).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }

    fn ceil(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x.ceil()).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }

    fn round(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x.round()).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }

    fn trunc(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x.trunc()).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }

    fn abs(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x.abs()).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }

    fn clamp(&self, min: T, max: T) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x.clamp(min, max)).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tensor::{zeros, ones, from_slice, Shape};

    #[test]
    fn test_trigonometric_functions() {
        let shape = Shape::new(vec![2, 2]);
        let data = vec![0.0, std::f32::consts::PI / 4.0, std::f32::consts::PI / 2.0, std::f32::consts::PI];
        let tensor = from_slice(&data, &shape).unwrap();

        let sin_result = tensor.sin().unwrap();
        let cos_result = tensor.cos().unwrap();

        // Check some known values
        let sin_data = sin_result.data();
        assert!((sin_data[0] - 0.0).abs() < 1e-6);
        assert!((sin_data[2] - 1.0).abs() < 1e-6);

        let cos_data = cos_result.data();
        assert!((cos_data[0] - 1.0).abs() < 1e-6);
        assert!((cos_data[2]).abs() < 1e-6);
    }

    #[test]
    fn test_exponential_functions() {
        let shape = Shape::new(vec![3]);
        let data = vec![0.0, 1.0, 2.0];
        let tensor = from_slice(&data, &shape).unwrap();

        let exp_result = tensor.exp().unwrap();
        let log_result = tensor.log().unwrap();

        let exp_data = exp_result.data();
        assert!((exp_data[0] - 1.0).abs() < 1e-6);
        assert!((exp_data[1] - std::f32::consts::E).abs() < 1e-6);

        // Test that exp(log(x)) = x for positive values
        let data_pos = vec![1.0, 2.0, 3.0];
        let tensor_pos = from_slice(&data_pos, &shape).unwrap();
        let log_exp = tensor_pos.log().unwrap().exp().unwrap();
        let log_exp_data = log_exp.data();

        for (i, &expected) in data_pos.iter().enumerate() {
            assert!((log_exp_data[i] - expected).abs() < 1e-6);
        }
    }

    #[test]
    fn test_rounding_functions() {
        let shape = Shape::new(vec![4]);
        let data = vec![1.2, 1.7, -1.2, -1.7];
        let tensor = from_slice(&data, &shape).unwrap();

        let floor_result = tensor.floor().unwrap();
        let ceil_result = tensor.ceil().unwrap();
        let round_result = tensor.round().unwrap();

        let floor_data = floor_result.data();
        assert_eq!(floor_data, &[1.0, 1.0, -2.0, -2.0]);

        let ceil_data = ceil_result.data();
        assert_eq!(ceil_data, &[2.0, 2.0, -1.0, -1.0]);

        let round_data = round_result.data();
        assert_eq!(round_data, &[1.0, 2.0, -1.0, -2.0]);
    }

    #[test]
    fn test_clamp() {
        let shape = Shape::new(vec![5]);
        let data = vec![-2.0, -1.0, 0.0, 1.0, 2.0];
        let tensor = from_slice(&data, &shape).unwrap();

        let clamped = tensor.clamp(-0.5, 0.5).unwrap();
        let clamped_data = clamped.data();

        assert_eq!(clamped_data, &[-0.5, -0.5, 0.0, 0.5, 0.5]);
    }

    #[test]
    fn test_power_functions() {
        let shape = Shape::new(vec![3]);
        let base_data = vec![2.0, 3.0, 4.0];
        let exp_data = vec![2.0, 3.0, 0.5];

        let base_tensor = from_slice(&base_data, &shape).unwrap();
        let exp_tensor = from_slice(&exp_data, &shape).unwrap();

        let pow_result = base_tensor.pow(&exp_tensor).unwrap();
        let pow_data = pow_result.data();

        assert!((pow_data[0] - 4.0).abs() < 1e-6);  // 2^2 = 4
        assert!((pow_data[1] - 27.0).abs() < 1e-6); // 3^3 = 27
        assert!((pow_data[2] - 2.0).abs() < 1e-6);  // 4^0.5 = 2

        // Test scalar power
        let pow_scalar = base_tensor.pow_scalar(2.0).unwrap();
        let pow_scalar_data = pow_scalar.data();
        assert_eq!(pow_scalar_data, &[4.0, 9.0, 16.0]);
    }

    #[test]
    fn test_max_min_operations() {
        let shape = Shape::new(vec![4]);
        let data = vec![1.0, 4.0, 2.0, 3.0];
        let tensor = from_slice(&data, &shape).unwrap();

        let max_result = tensor.max(None, false).unwrap();
        let min_result = tensor.min(None, false).unwrap();

        assert_eq!(max_result.data()[0], 4.0);
        assert_eq!(min_result.data()[0], 1.0);

        // Test keepdim
        let max_keepdim = tensor.max(None, true).unwrap();
        assert_eq!(max_keepdim.shape().dims(), &[1]);
        assert_eq!(max_keepdim.data()[0], 4.0);
    }
}
