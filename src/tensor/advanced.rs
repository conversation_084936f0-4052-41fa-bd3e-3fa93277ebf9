//! Advanced tensor operations.
//!
//! This module provides advanced tensor operations including:
//! - Broadcasting mechanism
//! - Tensor concatenation and stacking
//! - Indexing operations (gather, scatter, index_select)
//! - Masking operations (masked_fill, masked_select)
//! - Sorting operations (sort, argsort, topk)

use crate::tensor::{Tensor, TensorOps, Numeric, Shape};
use crate::tensor::cpu::CpuTensor;
use crate::error::{TensorError, ErrorContext};

/// Trait for advanced tensor operations.
///
/// This trait provides sophisticated tensor operations including concatenation, stacking,
/// indexing, masking, and sorting operations that are commonly used in deep learning
/// and scientific computing applications.
///
/// # Examples
///
/// ```rust
/// use qilin_inference::tensor::{CpuTensor, Shape, advanced::AdvancedOps};
///
/// let data1 = vec![1.0, 2.0, 3.0, 4.0];
/// let data2 = vec![5.0, 6.0, 7.0, 8.0];
/// let tensor1 = CpuTensor::from_data(data1, Shape::new(vec![2, 2])).unwrap();
/// let tensor2 = CpuTensor::from_data(data2, Shape::new(vec![2, 2])).unwrap();
///
/// // Concatenate along dimension 0
/// let concatenated = CpuTensor::cat(&[&tensor1, &tensor2], 0).unwrap();
///
/// // Sort tensor
/// let (sorted_values, sorted_indices) = tensor1.sort(1, false).unwrap();
/// ```
pub trait AdvancedOps<T: Numeric>: Tensor<T> {
    /// Concatenates tensors along an existing dimension.
    ///
    /// All tensors must have the same shape except in the concatenation dimension.
    ///
    /// # Arguments
    ///
    /// * `tensors` - Slice of tensor references to concatenate.
    /// * `dim` - The dimension along which to concatenate.
    ///
    /// # Returns
    ///
    /// A new tensor containing the concatenated data.
    ///
    /// # Examples
    ///
    /// ```rust
    /// # use qilin_inference::tensor::{CpuTensor, Shape, advanced::AdvancedOps};
    /// let data1 = vec![1.0, 2.0];
    /// let data2 = vec![3.0, 4.0];
    /// let tensor1 = CpuTensor::from_data(data1, Shape::new(vec![1, 2])).unwrap();
    /// let tensor2 = CpuTensor::from_data(data2, Shape::new(vec![1, 2])).unwrap();
    ///
    /// // Concatenate along dimension 0: result shape [2, 2]
    /// let result = CpuTensor::cat(&[&tensor1, &tensor2], 0).unwrap();
    /// // Result: [[1.0, 2.0], [3.0, 4.0]]
    ///
    /// // Concatenate along dimension 1: result shape [1, 4]
    /// let result = CpuTensor::cat(&[&tensor1, &tensor2], 1).unwrap();
    /// // Result: [[1.0, 2.0, 3.0, 4.0]]
    /// ```
    fn cat(tensors: &[&Self], dim: usize) -> Result<Self, TensorError>;

    /// Stacks tensors along a new dimension.
    ///
    /// All tensors must have exactly the same shape.
    ///
    /// # Arguments
    ///
    /// * `tensors` - Slice of tensor references to stack.
    /// * `dim` - The new dimension along which to stack (0 ≤ dim ≤ ndim).
    ///
    /// # Returns
    ///
    /// A new tensor with one additional dimension.
    ///
    /// # Examples
    ///
    /// ```rust
    /// # use qilin_inference::tensor::{CpuTensor, Shape, advanced::AdvancedOps};
    /// let data1 = vec![1.0, 2.0];
    /// let data2 = vec![3.0, 4.0];
    /// let tensor1 = CpuTensor::from_data(data1, Shape::new(vec![2])).unwrap();
    /// let tensor2 = CpuTensor::from_data(data2, Shape::new(vec![2])).unwrap();
    ///
    /// // Stack along dimension 0: result shape [2, 2]
    /// let result = CpuTensor::stack(&[&tensor1, &tensor2], 0).unwrap();
    /// // Result: [[1.0, 2.0], [3.0, 4.0]]
    ///
    /// // Stack along dimension 1: result shape [2, 2]
    /// let result = CpuTensor::stack(&[&tensor1, &tensor2], 1).unwrap();
    /// // Result: [[1.0, 3.0], [2.0, 4.0]]
    /// ```
    fn stack(tensors: &[&Self], dim: usize) -> Result<Self, TensorError>;

    /// Splits a tensor into chunks of a specified size along a dimension.
    ///
    /// # Arguments
    ///
    /// * `split_size` - The size of each chunk (except possibly the last one).
    /// * `dim` - The dimension along which to split.
    ///
    /// # Returns
    ///
    /// A vector of tensors representing the chunks.
    ///
    /// # Examples
    ///
    /// ```rust
    /// # use qilin_inference::tensor::{CpuTensor, Shape, advanced::AdvancedOps};
    /// let data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0];
    /// let tensor = CpuTensor::from_data(data, Shape::new(vec![6])).unwrap();
    ///
    /// // Split into chunks of size 2
    /// let chunks = tensor.split(2, 0).unwrap();
    /// // Result: 3 tensors with data [1.0, 2.0], [3.0, 4.0], [5.0, 6.0]
    ///
    /// // Split into chunks of size 4 (last chunk will be smaller)
    /// let chunks = tensor.split(4, 0).unwrap();
    /// // Result: 2 tensors with data [1.0, 2.0, 3.0, 4.0], [5.0, 6.0]
    /// ```
    fn split(&self, split_size: usize, dim: usize) -> Result<Vec<Self>, Self::Error>;

    /// Splits a tensor into a specified number of chunks along a dimension.
    ///
    /// # Arguments
    ///
    /// * `chunks` - The number of chunks to create.
    /// * `dim` - The dimension along which to split.
    ///
    /// # Returns
    ///
    /// A vector of tensors representing the chunks.
    ///
    /// # Examples
    ///
    /// ```rust
    /// # use qilin_inference::tensor::{CpuTensor, Shape, advanced::AdvancedOps};
    /// let data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0];
    /// let tensor = CpuTensor::from_data(data, Shape::new(vec![6])).unwrap();
    ///
    /// // Split into 3 chunks
    /// let chunks = tensor.chunk(3, 0).unwrap();
    /// // Result: 3 tensors with data [1.0, 2.0], [3.0, 4.0], [5.0, 6.0]
    ///
    /// // Split into 4 chunks (some chunks may be smaller)
    /// let chunks = tensor.chunk(4, 0).unwrap();
    /// // Result: 4 tensors with sizes [2, 2, 1, 1]
    /// ```
    fn chunk(&self, chunks: usize, dim: usize) -> Result<Vec<Self>, Self::Error>;

    /// Gathers values along a dimension using indices.
    ///
    /// # Arguments
    ///
    /// * `dim` - The dimension along which to gather.
    /// * `index` - Tensor containing indices to gather.
    ///
    /// # Returns
    ///
    /// A tensor with the same shape as the index tensor, containing gathered values.
    ///
    /// # Examples
    ///
    /// ```rust
    /// # use qilin_inference::tensor::{CpuTensor, Shape, advanced::AdvancedOps};
    /// let data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0];
    /// let tensor = CpuTensor::from_data(data, Shape::new(vec![2, 3])).unwrap();
    /// // Tensor: [[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]]
    ///
    /// let indices = vec![0.0, 2.0, 1.0];
    /// let index_tensor = CpuTensor::from_data(indices, Shape::new(vec![3])).unwrap();
    ///
    /// // Gather along dimension 1
    /// let result = tensor.gather(1, &index_tensor).unwrap();
    /// // Result: [1.0, 3.0, 2.0] (gathering columns 0, 2, 1 from first row)
    /// ```
    fn gather(&self, dim: usize, index: &Self) -> Result<Self, Self::Error>;

    /// Scatters values along a dimension using indices.
    ///
    /// # Arguments
    ///
    /// * `dim` - The dimension along which to scatter.
    /// * `index` - Tensor containing indices where to scatter.
    /// * `src` - Tensor containing values to scatter.
    ///
    /// # Returns
    ///
    /// A tensor with scattered values.
    ///
    /// # Examples
    ///
    /// ```rust
    /// # use qilin_inference::tensor::{CpuTensor, Shape, advanced::AdvancedOps};
    /// let data = vec![0.0; 6];
    /// let mut tensor = CpuTensor::from_data(data, Shape::new(vec![2, 3])).unwrap();
    ///
    /// let indices = vec![1.0, 0.0, 2.0];
    /// let index_tensor = CpuTensor::from_data(indices, Shape::new(vec![3])).unwrap();
    ///
    /// let values = vec![10.0, 20.0, 30.0];
    /// let src_tensor = CpuTensor::from_data(values, Shape::new(vec![3])).unwrap();
    ///
    /// // Scatter along dimension 1
    /// let result = tensor.scatter(1, &index_tensor, &src_tensor).unwrap();
    /// // Result: [[20.0, 10.0, 30.0], [0.0, 0.0, 0.0]]
    /// ```
    fn scatter(&self, dim: usize, index: &Self, src: &Self) -> Result<Self, Self::Error>;

    /// Fills tensor elements with a value where the mask is true.
    ///
    /// # Arguments
    ///
    /// * `mask` - Boolean tensor indicating where to fill values.
    /// * `value` - The value to fill where mask is true.
    ///
    /// # Returns
    ///
    /// A new tensor with values filled according to the mask.
    ///
    /// # Examples
    ///
    /// ```rust
    /// # use qilin_inference::tensor::{CpuTensor, Shape, advanced::AdvancedOps};
    /// let data = vec![1.0, 2.0, 3.0, 4.0];
    /// let tensor = CpuTensor::from_data(data, Shape::new(vec![2, 2])).unwrap();
    ///
    /// let mask_data = vec![1.0, 0.0, 1.0, 0.0]; // true, false, true, false
    /// let mask = CpuTensor::from_data(mask_data, Shape::new(vec![2, 2])).unwrap();
    ///
    /// let result = tensor.masked_fill(&mask, 99.0).unwrap();
    /// // Result: [[99.0, 2.0], [99.0, 4.0]]
    /// ```
    fn masked_fill(&self, mask: &Self, value: T) -> Result<Self, Self::Error>;

    /// Selects elements from the tensor where the mask is true.
    ///
    /// Returns a 1D tensor containing only the elements where the mask is true.
    ///
    /// # Arguments
    ///
    /// * `mask` - Boolean tensor indicating which elements to select.
    ///
    /// # Returns
    ///
    /// A 1D tensor containing the selected elements.
    ///
    /// # Examples
    ///
    /// ```rust
    /// # use qilin_inference::tensor::{CpuTensor, Shape, advanced::AdvancedOps};
    /// let data = vec![1.0, 2.0, 3.0, 4.0];
    /// let tensor = CpuTensor::from_data(data, Shape::new(vec![2, 2])).unwrap();
    ///
    /// let mask_data = vec![1.0, 0.0, 1.0, 0.0]; // true, false, true, false
    /// let mask = CpuTensor::from_data(mask_data, Shape::new(vec![2, 2])).unwrap();
    ///
    /// let result = tensor.masked_select(&mask).unwrap();
    /// // Result: [1.0, 3.0] (1D tensor with selected elements)
    /// ```
    fn masked_select(&self, mask: &Self) -> Result<Self, Self::Error>;

    /// Sorts the tensor along a specified dimension.
    ///
    /// # Arguments
    ///
    /// * `dim` - The dimension along which to sort.
    /// * `descending` - Whether to sort in descending order.
    ///
    /// # Returns
    ///
    /// A tuple of (sorted_values, sorted_indices) where sorted_values contains
    /// the sorted tensor and sorted_indices contains the original indices.
    ///
    /// # Examples
    ///
    /// ```rust
    /// # use qilin_inference::tensor::{CpuTensor, Shape, advanced::AdvancedOps};
    /// let data = vec![3.0, 1.0, 4.0, 2.0];
    /// let tensor = CpuTensor::from_data(data, Shape::new(vec![2, 2])).unwrap();
    /// // Tensor: [[3.0, 1.0], [4.0, 2.0]]
    ///
    /// // Sort along dimension 1 (columns within each row)
    /// let (sorted_values, sorted_indices) = tensor.sort(1, false).unwrap();
    /// // sorted_values: [[1.0, 3.0], [2.0, 4.0]]
    /// // sorted_indices: [[1, 0], [1, 0]]
    ///
    /// // Sort in descending order
    /// let (sorted_desc, indices_desc) = tensor.sort(1, true).unwrap();
    /// // sorted_desc: [[3.0, 1.0], [4.0, 2.0]]
    /// ```
    fn sort(&self, dim: usize, descending: bool) -> Result<(Self, Self), Self::Error>;

    /// Returns the indices that would sort the tensor along a dimension.
    ///
    /// # Arguments
    ///
    /// * `dim` - The dimension along which to compute sort indices.
    /// * `descending` - Whether to compute indices for descending order.
    ///
    /// # Returns
    ///
    /// A tensor containing the indices that would sort the input tensor.
    ///
    /// # Examples
    ///
    /// ```rust
    /// # use qilin_inference::tensor::{CpuTensor, Shape, advanced::AdvancedOps};
    /// let data = vec![3.0, 1.0, 4.0, 2.0];
    /// let tensor = CpuTensor::from_data(data, Shape::new(vec![4])).unwrap();
    ///
    /// let indices = tensor.argsort(0, false).unwrap();
    /// // Result: [1, 3, 0, 2] (indices that would sort [3,1,4,2] to [1,2,3,4])
    ///
    /// let indices_desc = tensor.argsort(0, true).unwrap();
    /// // Result: [2, 0, 3, 1] (indices for descending sort)
    /// ```
    fn argsort(&self, dim: usize, descending: bool) -> Result<Self, Self::Error>;

    /// Returns the top-k largest or smallest values and their indices along a dimension.
    ///
    /// # Arguments
    ///
    /// * `k` - The number of top values to return.
    /// * `dim` - The dimension along which to find top-k values.
    /// * `largest` - Whether to return the largest values (true) or smallest (false).
    ///
    /// # Returns
    ///
    /// A tuple of (values, indices) where values contains the top-k values
    /// and indices contains their original positions.
    ///
    /// # Examples
    ///
    /// ```rust
    /// # use qilin_inference::tensor::{CpuTensor, Shape, advanced::AdvancedOps};
    /// let data = vec![3.0, 1.0, 4.0, 2.0, 5.0];
    /// let tensor = CpuTensor::from_data(data, Shape::new(vec![5])).unwrap();
    ///
    /// // Get top 3 largest values
    /// let (top_values, top_indices) = tensor.topk(3, 0, true).unwrap();
    /// // top_values: [5.0, 4.0, 3.0]
    /// // top_indices: [4, 2, 0]
    ///
    /// // Get top 2 smallest values
    /// let (small_values, small_indices) = tensor.topk(2, 0, false).unwrap();
    /// // small_values: [1.0, 2.0]
    /// // small_indices: [1, 3]
    /// ```
    fn topk(&self, k: usize, dim: usize, largest: bool) -> Result<(Self, Self), Self::Error>;
}

/// Broadcasting utilities.
pub struct Broadcasting;

impl Broadcasting {
    /// Check if two shapes are broadcastable.
    pub fn are_broadcastable(shape1: &Shape, shape2: &Shape) -> bool {
        let dims1 = shape1.dims();
        let dims2 = shape2.dims();
        
        let max_rank = dims1.len().max(dims2.len());
        
        for i in 0..max_rank {
            let dim1 = if i < dims1.len() { dims1[dims1.len() - 1 - i] } else { 1 };
            let dim2 = if i < dims2.len() { dims2[dims2.len() - 1 - i] } else { 1 };
            
            if dim1 != dim2 && dim1 != 1 && dim2 != 1 {
                return false;
            }
        }
        
        true
    }
    
    /// Compute the broadcasted shape of two shapes.
    pub fn broadcast_shapes(shape1: &Shape, shape2: &Shape) -> Result<Shape, TensorError> {
        if !Self::are_broadcastable(shape1, shape2) {
            return Err(TensorError::ShapeMismatch {
                expected: shape1.dims().to_vec(),
                actual: shape2.dims().to_vec(),
                context: Some(ErrorContext::new("broadcast_shapes", "tensor::advanced")),
            });
        }
        
        let dims1 = shape1.dims();
        let dims2 = shape2.dims();
        let max_rank = dims1.len().max(dims2.len());
        let mut result_dims = vec![1; max_rank];
        
        for i in 0..max_rank {
            let dim1 = if i < dims1.len() { dims1[dims1.len() - 1 - i] } else { 1 };
            let dim2 = if i < dims2.len() { dims2[dims2.len() - 1 - i] } else { 1 };
            
            result_dims[max_rank - 1 - i] = dim1.max(dim2);
        }
        
        Ok(Shape::new(result_dims))
    }
    
    /// Broadcast a tensor to a target shape.
    pub fn broadcast_to<T: Numeric>(tensor: &CpuTensor<T>, target_shape: &Shape) -> Result<CpuTensor<T>, TensorError> {
        if tensor.shape() == target_shape {
            return Ok(tensor.clone());
        }
        
        if !Self::are_broadcastable(tensor.shape(), target_shape) {
            return Err(TensorError::ShapeMismatch {
                expected: target_shape.dims().to_vec(),
                actual: tensor.shape().dims().to_vec(),
                context: Some(ErrorContext::new("broadcast_to", "tensor::advanced")),
            });
        }
        
        let source_dims = tensor.shape().dims();
        let target_dims = target_shape.dims();
        let target_size = target_shape.size();
        let mut result = vec![T::ZERO; target_size];
        
        let source_data = tensor.data();
        let source_strides = tensor.shape().strides();
        
        // Calculate broadcasting strides
        let mut broadcast_strides = vec![0; target_dims.len()];
        let rank_diff = target_dims.len() - source_dims.len();
        
        for i in 0..source_dims.len() {
            let source_idx = i;
            let target_idx = i + rank_diff;
            
            if source_dims[source_idx] == target_dims[target_idx] {
                broadcast_strides[target_idx] = source_strides[source_idx];
            } else if source_dims[source_idx] == 1 {
                broadcast_strides[target_idx] = 0; // Broadcast this dimension
            } else {
                return Err(TensorError::ShapeMismatch {
                    expected: target_dims.to_vec(),
                    actual: source_dims.to_vec(),
                    context: Some(ErrorContext::new("broadcast_to", "tensor::advanced")),
                });
            }
        }
        
        // Fill the result tensor
        for target_idx in 0..target_size {
            let mut source_idx = 0;
            let mut remaining = target_idx;
            
            for (dim_idx, &dim_size) in target_dims.iter().enumerate().rev() {
                let coord = remaining % dim_size;
                remaining /= dim_size;
                source_idx += coord * broadcast_strides[dim_idx];
            }
            
            result[target_idx] = source_data[source_idx];
        }
        
        CpuTensor::from_data(result, target_shape.clone())
    }
}

impl<T: Numeric> AdvancedOps<T> for CpuTensor<T> {
    fn cat(tensors: &[&Self], dim: usize) -> Result<Self, TensorError> {
        if tensors.is_empty() {
            return Err(TensorError::InvalidDimension {
                dimension: 0,
                total_dims: 1,
                context: Some(ErrorContext::new("cat", "tensor::advanced")),
            });
        }
        
        let first_shape = tensors[0].shape();
        if dim >= first_shape.rank() {
            return Err(TensorError::InvalidDimension {
                dimension: dim,
                total_dims: first_shape.rank(),
                context: Some(ErrorContext::new("cat", "tensor::advanced")),
            });
        }
        
        // Check that all tensors have compatible shapes
        let mut total_dim_size = 0;
        for tensor in tensors {
            let shape = tensor.shape();
            if shape.rank() != first_shape.rank() {
                return Err(TensorError::ShapeMismatch {
                    expected: first_shape.dims().to_vec(),
                    actual: shape.dims().to_vec(),
                    context: Some(ErrorContext::new("cat", "tensor::advanced")),
                });
            }
            
            for (i, (&expected, &actual)) in first_shape.dims().iter().zip(shape.dims().iter()).enumerate() {
                if i != dim && expected != actual {
                    return Err(TensorError::ShapeMismatch {
                        expected: first_shape.dims().to_vec(),
                        actual: shape.dims().to_vec(),
                        context: Some(ErrorContext::new("cat", "tensor::advanced")),
                    });
                }
            }
            
            total_dim_size += shape.dims()[dim];
        }
        
        // Create output shape
        let mut output_dims = first_shape.dims().to_vec();
        output_dims[dim] = total_dim_size;
        let output_shape = Shape::new(output_dims);
        let output_size = output_shape.size();
        let mut result = vec![T::ZERO; output_size];
        
        // Copy data from each tensor
        let mut current_offset = 0;
        let output_strides = output_shape.strides();
        
        for tensor in tensors {
            let data = tensor.data();
            let shape = tensor.shape();
            let strides = shape.strides();
            let tensor_size = shape.size();
            
            // Copy tensor data to appropriate location in output
            for i in 0..tensor_size {
                // Convert flat index to coordinates
                let mut coords = vec![0; shape.rank()];
                let mut remaining = i;
                
                for (coord_idx, &dim_size) in shape.dims().iter().enumerate().rev() {
                    coords[coord_idx] = remaining % dim_size;
                    remaining /= dim_size;
                }
                
                // Adjust coordinate for concatenation dimension
                coords[dim] += current_offset;
                
                // Convert coordinates to output index
                let output_idx = coords.iter()
                    .zip(output_strides.iter())
                    .map(|(&coord, &stride)| coord * stride)
                    .sum::<usize>();
                
                result[output_idx] = data[i];
            }
            
            current_offset += shape.dims()[dim];
        }
        
        CpuTensor::from_data(result, output_shape)
    }
    
    fn stack(tensors: &[&Self], dim: usize) -> Result<Self, TensorError> {
        if tensors.is_empty() {
            return Err(TensorError::InvalidDimension {
                dimension: 0,
                total_dims: 1,
                context: Some(ErrorContext::new("stack", "tensor::advanced")),
            });
        }
        
        let first_shape = tensors[0].shape();
        if dim > first_shape.rank() {
            return Err(TensorError::InvalidDimension {
                dimension: dim,
                total_dims: first_shape.rank() + 1,
                context: Some(ErrorContext::new("stack", "tensor::advanced")),
            });
        }
        
        // Check that all tensors have the same shape
        for tensor in tensors.iter().skip(1) {
            if tensor.shape() != first_shape {
                return Err(TensorError::ShapeMismatch {
                    expected: first_shape.dims().to_vec(),
                    actual: tensor.shape().dims().to_vec(),
                    context: Some(ErrorContext::new("stack", "tensor::advanced")),
                });
            }
        }
        
        // Create output shape by inserting new dimension
        let mut output_dims = first_shape.dims().to_vec();
        output_dims.insert(dim, tensors.len());
        let output_shape = Shape::new(output_dims);
        let output_size = output_shape.size();
        let mut result = vec![T::ZERO; output_size];
        
        // Copy data from each tensor
        let tensor_size = first_shape.size();
        let output_strides = output_shape.strides();
        let stack_stride = output_strides[dim];
        
        for (stack_idx, tensor) in tensors.iter().enumerate() {
            let data = tensor.data();
            let base_offset = stack_idx * stack_stride;
            
            for i in 0..tensor_size {
                // Convert flat index to coordinates in original tensor
                let mut coords = vec![0; first_shape.rank()];
                let mut remaining = i;
                
                for (coord_idx, &dim_size) in first_shape.dims().iter().enumerate().rev() {
                    coords[coord_idx] = remaining % dim_size;
                    remaining /= dim_size;
                }
                
                // Insert stack coordinate
                coords.insert(dim, stack_idx);
                
                // Convert to output index
                let output_idx = coords.iter()
                    .zip(output_strides.iter())
                    .map(|(&coord, &stride)| coord * stride)
                    .sum::<usize>();
                
                result[output_idx] = data[i];
            }
        }
        
        CpuTensor::from_data(result, output_shape)
    }
    
    fn split(&self, split_size: usize, dim: usize) -> Result<Vec<Self>, Self::Error> {
        if dim >= self.shape().rank() {
            return Err(TensorError::InvalidDimension {
                dimension: dim,
                total_dims: self.shape().rank(),
                context: Some(ErrorContext::new("split", "tensor::advanced")),
            });
        }

        if split_size == 0 {
            return Err(TensorError::DataTypeIncompatible {
                operation: "split".to_string(),
                dtype: "split_size cannot be zero".to_string(),
                context: Some(ErrorContext::new("split", "tensor::advanced")),
            });
        }

        let dim_size = self.shape().dims()[dim];
        if dim_size == 0 {
            return Err(TensorError::DataTypeIncompatible {
                operation: "split".to_string(),
                dtype: "cannot split along dimension with size 0".to_string(),
                context: Some(ErrorContext::new("split", "tensor::advanced")),
            });
        }

        // Calculate number of chunks
        let num_chunks = (dim_size + split_size - 1) / split_size; // Ceiling division
        let mut result = Vec::with_capacity(num_chunks);

        for chunk_idx in 0..num_chunks {
            let start = chunk_idx * split_size;
            let end = std::cmp::min(start + split_size, dim_size);

            if start >= dim_size {
                break;
            }

            // Create slice ranges for this chunk
            let mut ranges = Vec::with_capacity(self.shape().rank());
            for (i, &size) in self.shape().dims().iter().enumerate() {
                if i == dim {
                    ranges.push(start..end);
                } else {
                    ranges.push(0..size);
                }
            }

            // Extract the slice
            let chunk = self.slice(&ranges)?;
            result.push(chunk);
        }

        Ok(result)
    }

    fn chunk(&self, chunks: usize, dim: usize) -> Result<Vec<Self>, Self::Error> {
        if dim >= self.shape().rank() {
            return Err(TensorError::InvalidDimension {
                dimension: dim,
                total_dims: self.shape().rank(),
                context: Some(ErrorContext::new("chunk", "tensor::advanced")),
            });
        }

        if chunks == 0 {
            return Err(TensorError::DataTypeIncompatible {
                operation: "chunk".to_string(),
                dtype: "number of chunks cannot be zero".to_string(),
                context: Some(ErrorContext::new("chunk", "tensor::advanced")),
            });
        }

        let dim_size = self.shape().dims()[dim];
        if dim_size == 0 {
            return Err(TensorError::DataTypeIncompatible {
                operation: "chunk".to_string(),
                dtype: "cannot chunk along dimension with size 0".to_string(),
                context: Some(ErrorContext::new("chunk", "tensor::advanced")),
            });
        }

        // Calculate chunk size (some chunks may be smaller if dim_size is not divisible by chunks)
        let base_chunk_size = dim_size / chunks;
        let remainder = dim_size % chunks;

        let mut result = Vec::with_capacity(chunks);
        let mut current_start = 0;

        for chunk_idx in 0..chunks {
            // First 'remainder' chunks get an extra element
            let chunk_size = if chunk_idx < remainder {
                base_chunk_size + 1
            } else {
                base_chunk_size
            };

            if chunk_size == 0 {
                break; // No more chunks needed
            }

            let end = current_start + chunk_size;

            // Create slice ranges for this chunk
            let mut ranges = Vec::with_capacity(self.shape().rank());
            for (i, &size) in self.shape().dims().iter().enumerate() {
                if i == dim {
                    ranges.push(current_start..end);
                } else {
                    ranges.push(0..size);
                }
            }

            // Extract the slice
            let chunk = self.slice(&ranges)?;
            result.push(chunk);

            current_start = end;
        }

        Ok(result)
    }

    fn gather(&self, dim: usize, index: &Self) -> Result<Self, Self::Error> {
        if dim >= self.shape().rank() {
            return Err(TensorError::InvalidDimension {
                dimension: dim,
                total_dims: self.shape().rank(),
                context: Some(ErrorContext::new("gather", "tensor::advanced")),
            });
        }

        // Index tensor must have the same number of dimensions as input tensor
        if index.shape().rank() != self.shape().rank() {
            return Err(TensorError::ShapeMismatch {
                expected: vec![self.shape().rank()],
                actual: vec![index.shape().rank()],
                context: Some(ErrorContext::new("gather", "tensor::advanced")
                    .with_info("reason", "index tensor must have same rank as input tensor")),
            });
        }

        // All dimensions except the gather dimension must be compatible
        // The index tensor can have different size in the gather dimension
        for i in 0..self.shape().rank() {
            if i != dim && index.shape().dims()[i] > self.shape().dims()[i] {
                return Err(TensorError::ShapeMismatch {
                    expected: self.shape().dims().to_vec(),
                    actual: index.shape().dims().to_vec(),
                    context: Some(ErrorContext::new("gather", "tensor::advanced")
                        .with_info("dimension", i.to_string())),
                });
            }
        }

        // Result tensor has the same shape as the index tensor
        let result_shape = index.shape().clone();
        let mut result_data = Vec::with_capacity(result_shape.size());

        // Iterate through all positions in the index tensor
        let mut indices = vec![0; result_shape.rank()];
        for _ in 0..result_shape.size() {
            // Get the index value at this position
            let mut flat_index = 0;
            for (i, &idx) in indices.iter().enumerate() {
                flat_index += idx * index.shape().strides()[i];
            }
            let gather_index = index.data()[flat_index];

            // Convert to usize and validate
            let gather_idx = gather_index.to_f32() as usize;
            if gather_idx >= self.shape().dims()[dim] {
                return Err(TensorError::IndexOutOfBounds {
                    index: gather_idx,
                    size: self.shape().dims()[dim],
                    context: Some(ErrorContext::new("gather", "tensor::advanced")
                        .with_info("dimension", dim.to_string())),
                });
            }

            // For gather, we need to create a slice to get the element at the specified indices
            let mut ranges = Vec::with_capacity(self.shape().rank());
            for (i, &idx) in indices.iter().enumerate() {
                if i == dim {
                    ranges.push(gather_idx..gather_idx + 1);
                } else {
                    ranges.push(idx..idx + 1);
                }
            }

            let slice = self.slice(&ranges)?;
            result_data.push(slice.data()[0]);

            // Increment indices (like an odometer)
            let mut carry = 1;
            for i in (0..indices.len()).rev() {
                indices[i] += carry;
                if indices[i] < result_shape.dims()[i] {
                    carry = 0;
                    break;
                } else {
                    indices[i] = 0;
                }
            }
            if carry == 1 {
                break; // We've processed all elements
            }
        }

        Self::from_data(result_data, result_shape)
    }

    fn scatter(&self, dim: usize, index: &Self, src: &Self) -> Result<Self, Self::Error> {
        if dim >= self.shape().rank() {
            return Err(TensorError::InvalidDimension {
                dimension: dim,
                total_dims: self.shape().rank(),
                context: Some(ErrorContext::new("scatter", "tensor::advanced")),
            });
        }

        // Index and src tensors must have the same shape
        if index.shape().dims() != src.shape().dims() {
            return Err(TensorError::ShapeMismatch {
                expected: index.shape().dims().to_vec(),
                actual: src.shape().dims().to_vec(),
                context: Some(ErrorContext::new("scatter", "tensor::advanced")
                    .with_info("reason", "index and src tensors must have same shape")),
            });
        }

        // Index tensor must have the same number of dimensions as input tensor
        if index.shape().rank() != self.shape().rank() {
            return Err(TensorError::ShapeMismatch {
                expected: vec![self.shape().rank()],
                actual: vec![index.shape().rank()],
                context: Some(ErrorContext::new("scatter", "tensor::advanced")
                    .with_info("reason", "index tensor must have same rank as input tensor")),
            });
        }

        // All dimensions except the scatter dimension must be compatible
        for i in 0..self.shape().rank() {
            if i != dim && index.shape().dims()[i] > self.shape().dims()[i] {
                return Err(TensorError::ShapeMismatch {
                    expected: self.shape().dims().to_vec(),
                    actual: index.shape().dims().to_vec(),
                    context: Some(ErrorContext::new("scatter", "tensor::advanced")
                        .with_info("dimension", i.to_string())),
                });
            }
        }

        // Start with a copy of the input tensor
        let mut result_data = self.data().to_vec();

        // Iterate through all positions in the index tensor
        let mut indices = vec![0; index.shape().rank()];
        for _ in 0..index.shape().size() {
            // Get the index value at this position
            let mut flat_index = 0;
            for (i, &idx) in indices.iter().enumerate() {
                flat_index += idx * index.shape().strides()[i];
            }
            let scatter_index = index.data()[flat_index];
            let src_value = src.data()[flat_index];

            // Convert to usize and validate
            let scatter_idx = scatter_index.to_f32() as usize;
            if scatter_idx >= self.shape().dims()[dim] {
                return Err(TensorError::IndexOutOfBounds {
                    index: scatter_idx,
                    size: self.shape().dims()[dim],
                    context: Some(ErrorContext::new("scatter", "tensor::advanced")
                        .with_info("dimension", dim.to_string())),
                });
            }

            // Calculate the target position by replacing the scatter dimension
            let mut target_indices = indices.clone();
            target_indices[dim] = scatter_idx;

            // Calculate flat index in result tensor using row-major order
            let mut target_flat_index = 0;
            for (i, &idx) in target_indices.iter().enumerate() {
                target_flat_index += idx * self.shape().strides()[i];
            }

            result_data[target_flat_index] = src_value;

            // Increment indices (like an odometer)
            let mut carry = 1;
            for i in (0..indices.len()).rev() {
                indices[i] += carry;
                if indices[i] < index.shape().dims()[i] {
                    carry = 0;
                    break;
                } else {
                    indices[i] = 0;
                }
            }
            if carry == 1 {
                break; // We've processed all elements
            }
        }

        Self::from_data(result_data, self.shape().clone())
    }
    
    fn masked_fill(&self, mask: &Self, value: T) -> Result<Self, Self::Error> {
        if self.shape() != mask.shape() {
            return Err(TensorError::ShapeMismatch {
                expected: self.shape().dims().to_vec(),
                actual: mask.shape().dims().to_vec(),
                context: Some(ErrorContext::new("masked_fill", "tensor::advanced")),
            });
        }
        
        let self_data = self.data();
        let mask_data = mask.data();
        let result_data: Vec<T> = self_data.iter()
            .zip(mask_data.iter())
            .map(|(&x, &m)| if m != T::ZERO { value } else { x })
            .collect();
        
        CpuTensor::from_data(result_data, self.shape().clone())
    }
    
    fn masked_select(&self, mask: &Self) -> Result<Self, Self::Error> {
        // Mask and input tensor must have the same shape
        if self.shape().dims() != mask.shape().dims() {
            return Err(TensorError::ShapeMismatch {
                expected: self.shape().dims().to_vec(),
                actual: mask.shape().dims().to_vec(),
                context: Some(ErrorContext::new("masked_select", "tensor::advanced")
                    .with_info("reason", "mask and input tensor must have same shape")),
            });
        }

        // Count the number of true elements in the mask
        let mut selected_count = 0;
        for &mask_value in mask.data() {
            if mask_value.to_f32() != 0.0 {
                selected_count += 1;
            }
        }

        // If no elements are selected, return empty 1D tensor
        if selected_count == 0 {
            return Self::from_data(Vec::new(), Shape::new(vec![0]));
        }

        // Collect selected elements
        let mut selected_data = Vec::with_capacity(selected_count);
        for (i, &mask_value) in mask.data().iter().enumerate() {
            if mask_value.to_f32() != 0.0 {
                selected_data.push(self.data()[i]);
            }
        }

        // Return as 1D tensor
        Self::from_data(selected_data, Shape::new(vec![selected_count]))
    }

    fn sort(&self, dim: usize, descending: bool) -> Result<(Self, Self), Self::Error> {
        if dim >= self.shape().rank() {
            return Err(TensorError::InvalidDimension {
                dimension: dim,
                total_dims: self.shape().rank(),
                context: Some(ErrorContext::new("sort", "tensor::advanced")),
            });
        }

        let mut sorted_data = self.data().to_vec();
        let mut indices_data = Vec::with_capacity(self.shape().size());

        // Initialize indices (0, 1, 2, ...)
        for i in 0..self.shape().size() {
            indices_data.push(T::from_f32(i as f32));
        }

        let dim_size = self.shape().dims()[dim];
        let outer_size = self.shape().size() / dim_size;
        let stride = self.shape().strides()[dim];

        // Sort each slice along the specified dimension
        for outer_idx in 0..outer_size {
            // Calculate the starting position for this slice
            let mut base_idx = 0;
            let mut remaining = outer_idx;

            for d in 0..self.shape().rank() {
                if d != dim {
                    let dim_stride = if d < dim {
                        self.shape().strides()[d] / stride
                    } else {
                        self.shape().strides()[d]
                    };
                    let coord = remaining % self.shape().dims()[d];
                    base_idx += coord * dim_stride;
                    remaining /= self.shape().dims()[d];
                }
            }

            // Collect (value, original_index) pairs for this slice
            let mut slice_pairs: Vec<(T, usize)> = Vec::with_capacity(dim_size);
            for i in 0..dim_size {
                let idx = base_idx + i * stride;
                slice_pairs.push((sorted_data[idx], i));
            }

            // Sort the slice
            if descending {
                slice_pairs.sort_by(|a, b| b.0.partial_cmp(&a.0).unwrap_or(std::cmp::Ordering::Equal));
            } else {
                slice_pairs.sort_by(|a, b| a.0.partial_cmp(&b.0).unwrap_or(std::cmp::Ordering::Equal));
            }

            // Write back sorted values and indices
            for (i, (value, original_idx)) in slice_pairs.iter().enumerate() {
                let idx = base_idx + i * stride;
                sorted_data[idx] = *value;
                indices_data[idx] = T::from_f32(*original_idx as f32);
            }
        }

        let sorted_tensor = Self::from_data(sorted_data, self.shape().clone())?;
        let indices_tensor = Self::from_data(indices_data, self.shape().clone())?;

        Ok((sorted_tensor, indices_tensor))
    }

    fn argsort(&self, dim: usize, descending: bool) -> Result<Self, Self::Error> {
        // argsort is just the indices part of sort
        let (_, indices) = self.sort(dim, descending)?;
        Ok(indices)
    }

    fn topk(&self, k: usize, dim: usize, largest: bool) -> Result<(Self, Self), Self::Error> {
        if dim >= self.shape().rank() {
            return Err(TensorError::InvalidDimension {
                dimension: dim,
                total_dims: self.shape().rank(),
                context: Some(ErrorContext::new("topk", "tensor::advanced")),
            });
        }

        let dim_size = self.shape().dims()[dim];
        if k > dim_size {
            return Err(TensorError::IndexOutOfBounds {
                index: k,
                size: dim_size,
                context: Some(ErrorContext::new("topk", "tensor::advanced")
                    .with_info("reason", "k cannot be larger than dimension size")),
            });
        }

        if k == 0 {
            return Err(TensorError::DataTypeIncompatible {
                operation: "topk".to_string(),
                dtype: "k cannot be zero".to_string(),
                context: Some(ErrorContext::new("topk", "tensor::advanced")),
            });
        }

        // Create new shape with reduced dimension size
        let mut new_dims = self.shape().dims().to_vec();
        new_dims[dim] = k;
        let new_shape = Shape::new(new_dims);

        let mut values_data = Vec::with_capacity(new_shape.size());
        let mut indices_data = Vec::with_capacity(new_shape.size());

        let outer_size = self.shape().size() / dim_size;
        let stride = self.shape().strides()[dim];

        // Process each slice along the specified dimension
        for outer_idx in 0..outer_size {
            // Calculate the starting position for this slice
            let mut base_idx = 0;
            let mut remaining = outer_idx;

            for d in 0..self.shape().rank() {
                if d != dim {
                    let dim_stride = if d < dim {
                        self.shape().strides()[d] / stride
                    } else {
                        self.shape().strides()[d]
                    };
                    let coord = remaining % self.shape().dims()[d];
                    base_idx += coord * dim_stride;
                    remaining /= self.shape().dims()[d];
                }
            }

            // Collect (value, original_index) pairs for this slice
            let mut slice_pairs: Vec<(T, usize)> = Vec::with_capacity(dim_size);
            for i in 0..dim_size {
                let idx = base_idx + i * stride;
                slice_pairs.push((self.data()[idx], i));
            }

            // Sort to get top-k
            if largest {
                slice_pairs.sort_by(|a, b| b.0.partial_cmp(&a.0).unwrap_or(std::cmp::Ordering::Equal));
            } else {
                slice_pairs.sort_by(|a, b| a.0.partial_cmp(&b.0).unwrap_or(std::cmp::Ordering::Equal));
            }

            // Take only the top k elements
            for i in 0..k {
                values_data.push(slice_pairs[i].0);
                indices_data.push(T::from_f32(slice_pairs[i].1 as f32));
            }
        }

        let values_tensor = Self::from_data(values_data, new_shape.clone())?;
        let indices_tensor = Self::from_data(indices_data, new_shape)?;

        Ok((values_tensor, indices_tensor))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tensor::{from_slice, Shape};

    #[test]
    fn test_broadcasting_compatibility() {
        let shape1 = Shape::new(vec![3, 1]);
        let shape2 = Shape::new(vec![1, 4]);

        assert!(Broadcasting::are_broadcastable(&shape1, &shape2));

        let result_shape = Broadcasting::broadcast_shapes(&shape1, &shape2).unwrap();
        assert_eq!(result_shape.dims(), &[3, 4]);
    }

    #[test]
    fn test_broadcasting_incompatible() {
        let shape1 = Shape::new(vec![3, 2]);
        let shape2 = Shape::new(vec![4, 2]);

        assert!(!Broadcasting::are_broadcastable(&shape1, &shape2));
        assert!(Broadcasting::broadcast_shapes(&shape1, &shape2).is_err());
    }

    #[test]
    fn test_broadcast_to() {
        let shape = Shape::new(vec![2, 1]);
        let data = vec![1.0, 2.0];
        let tensor = from_slice(&data, &shape).unwrap();

        let target_shape = Shape::new(vec![2, 3]);
        let broadcasted = Broadcasting::broadcast_to(&tensor, &target_shape).unwrap();

        assert_eq!(broadcasted.shape(), &target_shape);
        assert_eq!(broadcasted.data(), &[1.0, 1.0, 1.0, 2.0, 2.0, 2.0]);
    }

    #[test]
    fn test_cat_tensors() {
        let shape = Shape::new(vec![2, 2]);
        let data1 = vec![1.0, 2.0, 3.0, 4.0];
        let data2 = vec![5.0, 6.0, 7.0, 8.0];

        let tensor1 = from_slice(&data1, &shape).unwrap();
        let tensor2 = from_slice(&data2, &shape).unwrap();

        // Concatenate along dimension 0
        let cat_result = CpuTensor::cat(&[&tensor1, &tensor2], 0).unwrap();
        assert_eq!(cat_result.shape().dims(), &[4, 2]);
        assert_eq!(cat_result.data(), &[1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0]);

        // Concatenate along dimension 1
        let cat_result = CpuTensor::cat(&[&tensor1, &tensor2], 1).unwrap();
        assert_eq!(cat_result.shape().dims(), &[2, 4]);
        assert_eq!(cat_result.data(), &[1.0, 2.0, 5.0, 6.0, 3.0, 4.0, 7.0, 8.0]);
    }

    #[test]
    fn test_stack_tensors() {
        let shape = Shape::new(vec![2, 2]);
        let data1 = vec![1.0, 2.0, 3.0, 4.0];
        let data2 = vec![5.0, 6.0, 7.0, 8.0];

        let tensor1 = from_slice(&data1, &shape).unwrap();
        let tensor2 = from_slice(&data2, &shape).unwrap();

        // Stack along dimension 0 (new first dimension)
        let stack_result = CpuTensor::stack(&[&tensor1, &tensor2], 0).unwrap();
        assert_eq!(stack_result.shape().dims(), &[2, 2, 2]);
        assert_eq!(stack_result.data(), &[1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0]);

        // Stack along dimension 1 (new middle dimension)
        let stack_result = CpuTensor::stack(&[&tensor1, &tensor2], 1).unwrap();
        assert_eq!(stack_result.shape().dims(), &[2, 2, 2]);
        assert_eq!(stack_result.data(), &[1.0, 2.0, 5.0, 6.0, 3.0, 4.0, 7.0, 8.0]);
    }

    #[test]
    fn test_masked_fill() {
        let shape = Shape::new(vec![4]);
        let data = vec![1.0, 2.0, 3.0, 4.0];
        let mask_data = vec![1.0, 0.0, 1.0, 0.0]; // true, false, true, false

        let tensor = from_slice(&data, &shape).unwrap();
        let mask = from_slice(&mask_data, &shape).unwrap();

        let filled = tensor.masked_fill(&mask, 99.0).unwrap();
        assert_eq!(filled.data(), &[99.0, 2.0, 99.0, 4.0]);
    }

    #[test]
    fn test_cat_empty_tensors() {
        let empty_tensors: Vec<&CpuTensor<f32>> = vec![];
        let result = CpuTensor::cat(&empty_tensors, 0);
        assert!(result.is_err());
    }

    #[test]
    fn test_cat_incompatible_shapes() {
        let shape1 = Shape::new(vec![2, 2]);
        let shape2 = Shape::new(vec![3, 2]); // Different size in non-concat dimension

        let data1 = vec![1.0, 2.0, 3.0, 4.0];
        let data2 = vec![5.0, 6.0, 7.0, 8.0, 9.0, 10.0];

        let tensor1 = from_slice(&data1, &shape1).unwrap();
        let tensor2 = from_slice(&data2, &shape2).unwrap();

        // This should work for dimension 0 (concatenating along rows)
        let result = CpuTensor::cat(&[&tensor1, &tensor2], 0);
        assert!(result.is_ok());

        // This should fail for dimension 1 (different number of rows)
        let result = CpuTensor::cat(&[&tensor1, &tensor2], 1);
        assert!(result.is_err());
    }

    #[test]
    fn test_split_basic() {
        let tensor = from_slice(&[1.0, 2.0, 3.0, 4.0, 5.0, 6.0], &Shape::new(vec![2, 3])).unwrap();

        // Split along dimension 1 (columns) with split_size = 2
        let chunks = tensor.split(2, 1).unwrap();
        assert_eq!(chunks.len(), 2);

        // For a 2x3 tensor with data [1,2,3,4,5,6], the layout is row-major:
        // Row 0: [1, 2, 3]
        // Row 1: [4, 5, 6]
        // When we split along dimension 1 with size 2, we get:
        // First chunk: columns 0-1 -> [1, 2; 4, 5] -> data [1, 2, 4, 5]
        // Second chunk: column 2 -> [3; 6] -> data [3, 6]

        // First chunk should be 2x2
        assert_eq!(chunks[0].shape().dims(), &[2, 2]);
        assert_eq!(chunks[0].data(), &[1.0, 2.0, 4.0, 5.0]);

        // Second chunk should be 2x1
        assert_eq!(chunks[1].shape().dims(), &[2, 1]);
        assert_eq!(chunks[1].data(), &[3.0, 6.0]);
    }

    #[test]
    fn test_split_exact_division() {
        let tensor = from_slice(&[1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0], &Shape::new(vec![2, 4])).unwrap();

        // Split along dimension 1 with split_size = 2 (should create 2 equal chunks)
        let chunks = tensor.split(2, 1).unwrap();
        assert_eq!(chunks.len(), 2);

        // Both chunks should be 2x2
        assert_eq!(chunks[0].shape().dims(), &[2, 2]);
        assert_eq!(chunks[1].shape().dims(), &[2, 2]);

        assert_eq!(chunks[0].data(), &[1.0, 2.0, 5.0, 6.0]);
        assert_eq!(chunks[1].data(), &[3.0, 4.0, 7.0, 8.0]);
    }

    #[test]
    fn test_split_along_different_dimensions() {
        let tensor = from_slice(&[1.0, 2.0, 3.0, 4.0, 5.0, 6.0], &Shape::new(vec![3, 2])).unwrap();

        // Split along dimension 0 (rows) with split_size = 2
        let chunks = tensor.split(2, 0).unwrap();
        assert_eq!(chunks.len(), 2);

        // First chunk: first 2 rows
        assert_eq!(chunks[0].shape().dims(), &[2, 2]);
        assert_eq!(chunks[0].data(), &[1.0, 2.0, 3.0, 4.0]);

        // Second chunk: last row
        assert_eq!(chunks[1].shape().dims(), &[1, 2]);
        assert_eq!(chunks[1].data(), &[5.0, 6.0]);
    }

    #[test]
    fn test_chunk_basic() {
        let tensor = from_slice(&[1.0, 2.0, 3.0, 4.0, 5.0, 6.0], &Shape::new(vec![2, 3])).unwrap();

        // Chunk into 3 pieces along dimension 1
        let chunks = tensor.chunk(3, 1).unwrap();
        assert_eq!(chunks.len(), 3);

        // Each chunk should be 2x1
        for chunk in &chunks {
            assert_eq!(chunk.shape().dims(), &[2, 1]);
        }

        assert_eq!(chunks[0].data(), &[1.0, 4.0]); // First column
        assert_eq!(chunks[1].data(), &[2.0, 5.0]); // Second column
        assert_eq!(chunks[2].data(), &[3.0, 6.0]); // Third column
    }

    #[test]
    fn test_chunk_uneven_division() {
        let tensor = from_slice(&[1.0, 2.0, 3.0, 4.0, 5.0], &Shape::new(vec![5])).unwrap();

        // Chunk into 3 pieces (5 elements -> chunks of size 2, 2, 1)
        let chunks = tensor.chunk(3, 0).unwrap();
        assert_eq!(chunks.len(), 3);

        // First two chunks get 2 elements each, last chunk gets 1
        assert_eq!(chunks[0].shape().dims(), &[2]);
        assert_eq!(chunks[1].shape().dims(), &[2]);
        assert_eq!(chunks[2].shape().dims(), &[1]);

        assert_eq!(chunks[0].data(), &[1.0, 2.0]);
        assert_eq!(chunks[1].data(), &[3.0, 4.0]);
        assert_eq!(chunks[2].data(), &[5.0]);
    }

    #[test]
    fn test_chunk_more_chunks_than_elements() {
        let tensor = from_slice(&[1.0, 2.0, 3.0], &Shape::new(vec![3])).unwrap();

        // Request 5 chunks but only have 3 elements
        let chunks = tensor.chunk(5, 0).unwrap();
        assert_eq!(chunks.len(), 3); // Should only create 3 chunks

        // Each chunk should have 1 element
        for (i, chunk) in chunks.iter().enumerate() {
            assert_eq!(chunk.shape().dims(), &[1]);
            assert_eq!(chunk.data(), &[i as f32 + 1.0]);
        }
    }

    #[test]
    fn test_split_chunk_errors() {
        let tensor = from_slice(&[1.0, 2.0, 3.0, 4.0], &Shape::new(vec![2, 2])).unwrap();

        // Test invalid dimension
        assert!(tensor.split(1, 2).is_err()); // Dimension 2 doesn't exist
        assert!(tensor.chunk(2, 3).is_err()); // Dimension 3 doesn't exist

        // Test zero split_size/chunks
        assert!(tensor.split(0, 0).is_err());
        assert!(tensor.chunk(0, 0).is_err());
    }

    #[test]
    fn test_split_chunk_single_element() {
        let tensor = from_slice(&[42.0], &Shape::new(vec![1])).unwrap();

        // Split single element
        let split_result = tensor.split(1, 0).unwrap();
        assert_eq!(split_result.len(), 1);
        assert_eq!(split_result[0].data(), &[42.0]);

        // Chunk single element
        let chunk_result = tensor.chunk(1, 0).unwrap();
        assert_eq!(chunk_result.len(), 1);
        assert_eq!(chunk_result[0].data(), &[42.0]);
    }

    #[test]
    fn test_split_chunk_3d_tensor() {
        let data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 12.0];
        let tensor = from_slice(&data, &Shape::new(vec![2, 2, 3])).unwrap();

        // Split along last dimension
        let chunks = tensor.split(2, 2).unwrap();
        assert_eq!(chunks.len(), 2);

        // First chunk: 2x2x2
        assert_eq!(chunks[0].shape().dims(), &[2, 2, 2]);
        // Second chunk: 2x2x1
        assert_eq!(chunks[1].shape().dims(), &[2, 2, 1]);

        // Chunk along middle dimension
        let chunks = tensor.chunk(2, 1).unwrap();
        assert_eq!(chunks.len(), 2);

        // Both chunks: 2x1x3
        assert_eq!(chunks[0].shape().dims(), &[2, 1, 3]);
        assert_eq!(chunks[1].shape().dims(), &[2, 1, 3]);
    }

    #[test]
    fn test_gather_basic() {
        // Create a 2x3 tensor: [[1, 2, 3], [4, 5, 6]]
        let tensor = from_slice(&[1.0, 2.0, 3.0, 4.0, 5.0, 6.0], &Shape::new(vec![2, 3])).unwrap();

        // Create index tensor to gather columns 2, 0, 1
        let index = from_slice(&[2.0, 0.0, 1.0, 2.0, 0.0, 1.0], &Shape::new(vec![2, 3])).unwrap();

        // Gather along dimension 1 (columns)
        let result = tensor.gather(1, &index).unwrap();

        // Result should be [[3, 1, 2], [6, 4, 5]]
        assert_eq!(result.shape().dims(), &[2, 3]);
        assert_eq!(result.data(), &[3.0, 1.0, 2.0, 6.0, 4.0, 5.0]);
    }

    #[test]
    fn test_gather_different_dimensions() {
        // Create a 3x2 tensor: [[1, 2], [3, 4], [5, 6]]
        let tensor = from_slice(&[1.0, 2.0, 3.0, 4.0, 5.0, 6.0], &Shape::new(vec![3, 2])).unwrap();

        // Create index tensor to gather rows 2, 0
        let index = from_slice(&[2.0, 0.0, 2.0, 0.0], &Shape::new(vec![2, 2])).unwrap();

        // Gather along dimension 0 (rows)
        let result = tensor.gather(0, &index).unwrap();

        // For gather along dimension 0 with index [[2, 0], [2, 0]]:
        // Position (0,0): index 2 -> row 2, col 0 -> 5
        // Position (0,1): index 0 -> row 0, col 1 -> 2
        // Position (1,0): index 2 -> row 2, col 0 -> 5
        // Position (1,1): index 0 -> row 0, col 1 -> 2
        // Result should be [[5, 2], [5, 2]]
        assert_eq!(result.shape().dims(), &[2, 2]);
        assert_eq!(result.data(), &[5.0, 2.0, 5.0, 2.0]);
    }

    #[test]
    fn test_scatter_basic() {
        // Create a 2x3 tensor: [[1, 2, 3], [4, 5, 6]]
        let tensor = from_slice(&[1.0, 2.0, 3.0, 4.0, 5.0, 6.0], &Shape::new(vec![2, 3])).unwrap();

        // Create index tensor to scatter to columns 1, 0
        let index = from_slice(&[1.0, 0.0, 1.0, 0.0], &Shape::new(vec![2, 2])).unwrap();

        // Create source tensor with values to scatter
        let src = from_slice(&[10.0, 20.0, 30.0, 40.0], &Shape::new(vec![2, 2])).unwrap();

        // Scatter along dimension 1 (columns)
        let result = tensor.scatter(1, &index, &src).unwrap();

        // Result should be [[20, 10, 3], [40, 30, 6]]
        assert_eq!(result.shape().dims(), &[2, 3]);
        assert_eq!(result.data(), &[20.0, 10.0, 3.0, 40.0, 30.0, 6.0]);
    }

    #[test]
    fn test_scatter_different_dimensions() {
        // Create a 3x2 tensor: [[1, 2], [3, 4], [5, 6]]
        let tensor = from_slice(&[1.0, 2.0, 3.0, 4.0, 5.0, 6.0], &Shape::new(vec![3, 2])).unwrap();

        // Create index tensor to scatter to rows 0, 2
        let index = from_slice(&[0.0, 2.0], &Shape::new(vec![1, 2])).unwrap();

        // Create source tensor with values to scatter
        let src = from_slice(&[10.0, 20.0], &Shape::new(vec![1, 2])).unwrap();

        // Scatter along dimension 0 (rows)
        let result = tensor.scatter(0, &index, &src).unwrap();

        // For scatter along dimension 0 with index [0, 2] and src [10, 20]:
        // Position (0,0): index 0, value 10 -> scatter to (0,0) -> row 0, col 0
        // Position (0,1): index 2, value 20 -> scatter to (2,1) -> row 2, col 1
        // Result should be [[10, 2], [3, 4], [5, 20]]
        assert_eq!(result.shape().dims(), &[3, 2]);
        assert_eq!(result.data(), &[10.0, 2.0, 3.0, 4.0, 5.0, 20.0]);
    }

    #[test]
    fn test_gather_scatter_errors() {
        let tensor = from_slice(&[1.0, 2.0, 3.0, 4.0], &Shape::new(vec![2, 2])).unwrap();

        // Test invalid dimension
        let index = from_slice(&[0.0, 1.0], &Shape::new(vec![1, 2])).unwrap();
        assert!(tensor.gather(2, &index).is_err()); // Dimension 2 doesn't exist

        // Test shape mismatch for gather
        let wrong_index = from_slice(&[0.0, 1.0, 0.0], &Shape::new(vec![3])).unwrap();
        assert!(tensor.gather(0, &wrong_index).is_err()); // Wrong rank

        // Test index out of bounds
        let bad_index = from_slice(&[0.0, 5.0, 0.0, 1.0], &Shape::new(vec![2, 2])).unwrap();
        assert!(tensor.gather(1, &bad_index).is_err()); // Index 5 >= size 2

        // Test scatter errors
        let src = from_slice(&[10.0, 20.0], &Shape::new(vec![1, 2])).unwrap();
        assert!(tensor.scatter(2, &index, &src).is_err()); // Invalid dimension
        assert!(tensor.scatter(0, &bad_index, &tensor).is_err()); // Index out of bounds
    }

    #[test]
    fn test_gather_scatter_1d() {
        // Test with 1D tensors
        let tensor = from_slice(&[10.0, 20.0, 30.0, 40.0, 50.0], &Shape::new(vec![5])).unwrap();

        // Gather elements at indices 4, 1, 3
        let index = from_slice(&[4.0, 1.0, 3.0], &Shape::new(vec![3])).unwrap();
        let gathered = tensor.gather(0, &index).unwrap();
        assert_eq!(gathered.data(), &[50.0, 20.0, 40.0]);

        // Scatter values back
        let src = from_slice(&[100.0, 200.0, 300.0], &Shape::new(vec![3])).unwrap();
        let scattered = tensor.scatter(0, &index, &src).unwrap();
        assert_eq!(scattered.data(), &[10.0, 200.0, 30.0, 300.0, 100.0]);
    }

    #[test]
    fn test_sort_basic() {
        // Create a 2x3 tensor: [[3, 1, 4], [2, 6, 5]]
        let tensor = from_slice(&[3.0, 1.0, 4.0, 2.0, 6.0, 5.0], &Shape::new(vec![2, 3])).unwrap();

        // Sort along dimension 1 (columns) in ascending order
        let (sorted, indices) = tensor.sort(1, false).unwrap();

        // Expected sorted: [[1, 3, 4], [2, 5, 6]]
        assert_eq!(sorted.shape().dims(), &[2, 3]);
        assert_eq!(sorted.data(), &[1.0, 3.0, 4.0, 2.0, 5.0, 6.0]);

        // Expected indices: [[1, 0, 2], [0, 2, 1]]
        assert_eq!(indices.data(), &[1.0, 0.0, 2.0, 0.0, 2.0, 1.0]);
    }

    #[test]
    fn test_sort_descending() {
        // Create a 1D tensor: [3, 1, 4, 1, 5]
        let tensor = from_slice(&[3.0, 1.0, 4.0, 1.0, 5.0], &Shape::new(vec![5])).unwrap();

        // Sort in descending order
        let (sorted, indices) = tensor.sort(0, true).unwrap();

        // Expected sorted: [5, 4, 3, 1, 1]
        assert_eq!(sorted.data(), &[5.0, 4.0, 3.0, 1.0, 1.0]);

        // Expected indices: [4, 2, 0, 1, 3] (or [4, 2, 0, 3, 1] - both valid for equal elements)
        let indices_data = indices.data();
        assert_eq!(indices_data[0], 4.0); // 5 was at index 4
        assert_eq!(indices_data[1], 2.0); // 4 was at index 2
        assert_eq!(indices_data[2], 0.0); // 3 was at index 0
        // indices[3] and indices[4] should be 1.0 and 3.0 in some order (both had value 1)
        assert!(indices_data[3] == 1.0 || indices_data[3] == 3.0);
        assert!(indices_data[4] == 1.0 || indices_data[4] == 3.0);
    }

    #[test]
    fn test_argsort() {
        // Create a 2x3 tensor: [[3, 1, 4], [2, 6, 5]]
        let tensor = from_slice(&[3.0, 1.0, 4.0, 2.0, 6.0, 5.0], &Shape::new(vec![2, 3])).unwrap();

        // Get argsort along dimension 1
        let indices = tensor.argsort(1, false).unwrap();

        // Expected indices: [[1, 0, 2], [0, 2, 1]]
        assert_eq!(indices.shape().dims(), &[2, 3]);
        assert_eq!(indices.data(), &[1.0, 0.0, 2.0, 0.0, 2.0, 1.0]);
    }

    #[test]
    fn test_topk_largest() {
        // Create a 2x4 tensor: [[3, 1, 4, 2], [6, 5, 8, 7]]
        let tensor = from_slice(&[3.0, 1.0, 4.0, 2.0, 6.0, 5.0, 8.0, 7.0], &Shape::new(vec![2, 4])).unwrap();

        // Get top-2 largest along dimension 1
        let (values, indices) = tensor.topk(2, 1, true).unwrap();

        // Expected values: [[4, 3], [8, 7]]
        assert_eq!(values.shape().dims(), &[2, 2]);
        assert_eq!(values.data(), &[4.0, 3.0, 8.0, 7.0]);

        // Expected indices: [[2, 0], [2, 3]]
        assert_eq!(indices.data(), &[2.0, 0.0, 2.0, 3.0]);
    }

    #[test]
    fn test_topk_smallest() {
        // Create a 1D tensor: [3, 1, 4, 2, 5]
        let tensor = from_slice(&[3.0, 1.0, 4.0, 2.0, 5.0], &Shape::new(vec![5])).unwrap();

        // Get top-3 smallest
        let (values, indices) = tensor.topk(3, 0, false).unwrap();

        // Expected values: [1, 2, 3]
        assert_eq!(values.shape().dims(), &[3]);
        assert_eq!(values.data(), &[1.0, 2.0, 3.0]);

        // Expected indices: [1, 3, 0]
        assert_eq!(indices.data(), &[1.0, 3.0, 0.0]);
    }

    #[test]
    fn test_sort_topk_errors() {
        let tensor = from_slice(&[1.0, 2.0, 3.0, 4.0], &Shape::new(vec![2, 2])).unwrap();

        // Test invalid dimension
        assert!(tensor.sort(2, false).is_err()); // Dimension 2 doesn't exist
        assert!(tensor.argsort(2, false).is_err());
        assert!(tensor.topk(1, 2, true).is_err());

        // Test k > dimension size
        assert!(tensor.topk(3, 1, true).is_err()); // k=3 > dim_size=2

        // Test k = 0
        assert!(tensor.topk(0, 1, true).is_err());
    }

    #[test]
    fn test_masked_select_basic() {
        // Create a 2x3 tensor: [[1, 2, 3], [4, 5, 6]]
        let tensor = from_slice(&[1.0, 2.0, 3.0, 4.0, 5.0, 6.0], &Shape::new(vec![2, 3])).unwrap();

        // Create mask: [[true, false, true], [false, true, false]]
        let mask = from_slice(&[1.0, 0.0, 1.0, 0.0, 1.0, 0.0], &Shape::new(vec![2, 3])).unwrap();

        // Select elements where mask is true
        let result = tensor.masked_select(&mask).unwrap();

        // Should select elements at positions (0,0), (0,2), (1,1) -> [1, 3, 5]
        assert_eq!(result.shape().dims(), &[3]);
        assert_eq!(result.data(), &[1.0, 3.0, 5.0]);
    }

    #[test]
    fn test_masked_select_all_false() {
        // Create a 2x2 tensor: [[1, 2], [3, 4]]
        let tensor = from_slice(&[1.0, 2.0, 3.0, 4.0], &Shape::new(vec![2, 2])).unwrap();

        // Create mask with all false values
        let mask = from_slice(&[0.0, 0.0, 0.0, 0.0], &Shape::new(vec![2, 2])).unwrap();

        // Select elements where mask is true
        let result = tensor.masked_select(&mask).unwrap();

        // Should return empty 1D tensor
        assert_eq!(result.shape().dims(), &[0]);
        assert_eq!(result.data().len(), 0);
    }

    #[test]
    fn test_masked_select_all_true() {
        // Create a 1D tensor: [1, 2, 3, 4]
        let tensor = from_slice(&[1.0, 2.0, 3.0, 4.0], &Shape::new(vec![4])).unwrap();

        // Create mask with all true values
        let mask = from_slice(&[1.0, 1.0, 1.0, 1.0], &Shape::new(vec![4])).unwrap();

        // Select elements where mask is true
        let result = tensor.masked_select(&mask).unwrap();

        // Should return all elements as 1D tensor
        assert_eq!(result.shape().dims(), &[4]);
        assert_eq!(result.data(), &[1.0, 2.0, 3.0, 4.0]);
    }

    #[test]
    fn test_masked_select_errors() {
        let tensor = from_slice(&[1.0, 2.0, 3.0, 4.0], &Shape::new(vec![2, 2])).unwrap();

        // Test shape mismatch
        let wrong_mask = from_slice(&[1.0, 0.0, 1.0], &Shape::new(vec![3])).unwrap();
        assert!(tensor.masked_select(&wrong_mask).is_err());

        // Test different rank
        let wrong_rank_mask = from_slice(&[1.0, 0.0, 1.0, 0.0], &Shape::new(vec![1, 4])).unwrap();
        assert!(tensor.masked_select(&wrong_rank_mask).is_err());
    }

    #[test]
    fn test_advanced_operations_integration() {
        // Create a test tensor: [[5, 2, 8, 1], [3, 7, 4, 6]]
        let tensor = from_slice(&[5.0, 2.0, 8.0, 1.0, 3.0, 7.0, 4.0, 6.0], &Shape::new(vec![2, 4])).unwrap();

        // Test 1: Split and then gather
        let chunks = tensor.split(2, 1).unwrap(); // Split into chunks of size 2 along dim 1
        assert_eq!(chunks.len(), 2);
        assert_eq!(chunks[0].shape().dims(), &[2, 2]); // [[5, 2], [3, 7]]
        assert_eq!(chunks[1].shape().dims(), &[2, 2]); // [[8, 1], [4, 6]]

        // Gather from first chunk using indices [1, 0]
        let index = from_slice(&[1.0, 0.0, 1.0, 0.0], &Shape::new(vec![2, 2])).unwrap();
        let gathered = chunks[0].gather(1, &index).unwrap();
        assert_eq!(gathered.data(), &[2.0, 5.0, 7.0, 3.0]); // [[2, 5], [7, 3]]

        // Test 2: Sort and then topk
        let (sorted, sort_indices) = tensor.sort(1, false).unwrap(); // Sort ascending along dim 1
        assert_eq!(sorted.data(), &[1.0, 2.0, 5.0, 8.0, 3.0, 4.0, 6.0, 7.0]);

        let (topk_values, topk_indices) = tensor.topk(2, 1, true).unwrap(); // Top 2 largest
        assert_eq!(topk_values.data(), &[8.0, 5.0, 7.0, 6.0]); // [[8, 5], [7, 6]]

        // Test 3: Masked select after operations
        let mask = from_slice(&[1.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 1.0], &Shape::new(vec![2, 4])).unwrap();
        let selected = tensor.masked_select(&mask).unwrap();
        assert_eq!(selected.data(), &[5.0, 8.0, 7.0, 6.0]); // Elements at positions (0,0), (0,2), (1,1), (1,3)

        // Test 4: Scatter after gather
        let src = from_slice(&[10.0, 20.0, 30.0, 40.0], &Shape::new(vec![2, 2])).unwrap();
        let scatter_index = from_slice(&[0.0, 3.0, 1.0, 2.0], &Shape::new(vec![2, 2])).unwrap();
        let scattered = tensor.scatter(1, &scatter_index, &src).unwrap();
        // Original: [[5, 2, 8, 1], [3, 7, 4, 6]]
        // Scatter: pos(0,0)->col 0, pos(0,1)->col 3, pos(1,0)->col 1, pos(1,1)->col 2
        // Result: [[10, 2, 8, 20], [3, 30, 40, 6]]
        assert_eq!(scattered.data(), &[10.0, 2.0, 8.0, 20.0, 3.0, 30.0, 40.0, 6.0]);
    }

    #[test]
    fn test_edge_cases_and_performance() {
        // Test with large tensor to verify performance
        let size = 1000;
        let data: Vec<f32> = (0..size).map(|i| i as f32).collect();
        let large_tensor = from_slice(&data, &Shape::new(vec![size])).unwrap();

        // Test sort performance
        let (sorted, _) = large_tensor.sort(0, false).unwrap();
        assert_eq!(sorted.data()[0], 0.0);
        assert_eq!(sorted.data()[size - 1], (size - 1) as f32);

        // Test topk performance
        let (top10, _) = large_tensor.topk(10, 0, true).unwrap();
        assert_eq!(top10.shape().dims(), &[10]);
        assert_eq!(top10.data()[0], (size - 1) as f32); // Largest value

        // Test masked_select with sparse mask
        let mask_data: Vec<f32> = (0..size).map(|i| if i % 100 == 0 { 1.0 } else { 0.0 }).collect();
        let sparse_mask = from_slice(&mask_data, &Shape::new(vec![size])).unwrap();
        let selected = large_tensor.masked_select(&sparse_mask).unwrap();
        assert_eq!(selected.shape().dims(), &[10]); // Every 100th element
        assert_eq!(selected.data()[0], 0.0);
        assert_eq!(selected.data()[9], 900.0);
    }

    #[test]
    fn test_numerical_stability() {
        // Test with very small and very large numbers
        let tensor = from_slice(&[1e-10, 1e10, -1e10, -1e-10, 0.0], &Shape::new(vec![5])).unwrap();

        // Test sort with extreme values
        let (sorted, indices) = tensor.sort(0, false).unwrap();
        assert_eq!(sorted.data()[0], -1e10);
        assert_eq!(sorted.data()[4], 1e10);

        // Test topk with extreme values
        let (top2, _) = tensor.topk(2, 0, true).unwrap();
        assert_eq!(top2.data()[0], 1e10);
        assert_eq!(top2.data()[1], 1e-10);

        // Test masked_select with zero threshold
        let mask = from_slice(&[1.0, 1.0, 0.0, 1.0, 0.0], &Shape::new(vec![5])).unwrap();
        let selected = tensor.masked_select(&mask).unwrap();
        assert_eq!(selected.shape().dims(), &[3]);
        assert_eq!(selected.data(), &[1e-10, 1e10, -1e-10]);
    }
}
