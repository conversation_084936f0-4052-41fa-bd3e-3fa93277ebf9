//! Parallel computing support for tensor operations.
//!
//! This module provides parallel implementations of tensor operations using Rayon
//! for improved performance on multi-core systems. Parallel operations can provide
//! significant speedups for large tensors by distributing work across multiple CPU cores.
//!
//! # Features
//!
//! - Automatic parallelization based on tensor size thresholds
//! - Configurable chunk sizes for optimal load balancing
//! - Thread pool management with customizable thread limits
//! - Work-stealing scheduler for efficient CPU utilization
//!
//! # Performance Guidelines
//!
//! - Parallel operations are most beneficial for large tensors (>10,000 elements)
//! - Overhead of thread coordination can reduce performance for small tensors
//! - Optimal chunk size depends on operation complexity and system characteristics
//! - Memory bandwidth can become a bottleneck for simple operations
//!
//! # Examples
//!
//! ```rust
//! use qilin_inference::tensor::parallel::{ParallelConfig, ParallelOps};
//!
//! // Create configuration for parallel execution
//! let config = ParallelConfig::new(5_000, Some(4), 1_000);
//!
//! // Check if operation should be parallelized
//! let should_parallel = config.should_parallelize(50_000); // true
//!
//! // Get optimal chunk size
//! let chunk_size = config.optimal_chunk_size(50_000);
//! ```

use crate::tensor::Numeric;
use rayon::prelude::*;

/// Configuration for parallel execution of tensor operations.
///
/// This struct controls when and how parallel execution is performed,
/// allowing fine-tuning of performance characteristics based on the
/// specific use case and system capabilities.
///
/// # Thread Safety
///
/// ParallelConfig is thread-safe and can be shared across multiple threads.
/// The configuration is immutable once created, ensuring consistent behavior
/// in concurrent environments.
///
/// # Performance Tuning
///
/// The optimal configuration depends on several factors:
/// - **parallel_threshold**: Lower values increase parallelization but add overhead
/// - **max_threads**: Should typically match or be slightly less than CPU cores
/// - **chunk_size**: Larger chunks reduce overhead but may cause load imbalance
#[derive(Debug, Clone)]
pub struct ParallelConfig {
    /// Minimum number of elements to trigger parallel execution.
    ///
    /// Operations on tensors smaller than this threshold will use sequential
    /// execution to avoid the overhead of thread coordination.
    ///
    /// **Recommended values:**
    /// - Simple operations (add, mul): 10,000 - 50,000
    /// - Complex operations (sin, exp): 1,000 - 10,000
    /// - Memory-bound operations: 50,000 - 100,000
    pub parallel_threshold: usize,

    /// Maximum number of threads to use for parallel execution.
    ///
    /// - `None`: Use all available CPU cores (recommended for most cases)
    /// - `Some(n)`: Limit to n threads (useful for shared systems)
    ///
    /// **Guidelines:**
    /// - For CPU-bound operations: Use all cores
    /// - For memory-bound operations: Use 50-75% of cores
    /// - For shared systems: Limit to avoid resource contention
    pub max_threads: Option<usize>,

    /// Target chunk size for parallel iteration.
    ///
    /// Work is divided into chunks of approximately this size for distribution
    /// across threads. The actual chunk size may be adjusted based on the
    /// total work size and number of available threads.
    ///
    /// **Trade-offs:**
    /// - Larger chunks: Less overhead, potential load imbalance
    /// - Smaller chunks: Better load balancing, more overhead
    /// - Optimal range: 100 - 10,000 elements per chunk
    pub chunk_size: usize,
}

impl Default for ParallelConfig {
    fn default() -> Self {
        Self {
            parallel_threshold: 10_000, // Only parallelize for large tensors
            max_threads: None,          // Use all available cores
            chunk_size: 1_000,         // Process 1000 elements per chunk
        }
    }
}

impl ParallelConfig {
    /// Create a new parallel configuration.
    pub fn new(parallel_threshold: usize, max_threads: Option<usize>, chunk_size: usize) -> Self {
        Self {
            parallel_threshold,
            max_threads,
            chunk_size,
        }
    }
    
    /// Check if parallel execution should be used for the given size.
    pub fn should_parallelize(&self, size: usize) -> bool {
        size >= self.parallel_threshold
    }
    
    /// Get the optimal chunk size for the given total size.
    pub fn optimal_chunk_size(&self, total_size: usize) -> usize {
        if total_size < self.parallel_threshold {
            total_size
        } else {
            let num_cores = num_cpus::get();
            let calculated_chunk = total_size / num_cores;
            std::cmp::max(1, std::cmp::min(self.chunk_size, calculated_chunk))
        }
    }
}

/// Parallel operations for tensor data.
pub struct ParallelOps;

impl ParallelOps {
    /// Parallel element-wise addition for f32 slices.
    pub fn add_f32_parallel(a: &[f32], b: &[f32], result: &mut [f32], config: &ParallelConfig) {
        assert_eq!(a.len(), b.len());
        assert_eq!(a.len(), result.len());
        
        if config.should_parallelize(a.len()) {
            let chunk_size = config.optimal_chunk_size(a.len());
            
            a.par_chunks(chunk_size)
                .zip(b.par_chunks(chunk_size))
                .zip(result.par_chunks_mut(chunk_size))
                .for_each(|((a_chunk, b_chunk), result_chunk)| {
                    for ((a_val, b_val), res) in a_chunk.iter().zip(b_chunk.iter()).zip(result_chunk.iter_mut()) {
                        *res = a_val + b_val;
                    }
                });
        } else {
            // Use sequential implementation for small arrays
            for ((a_val, b_val), res) in a.iter().zip(b.iter()).zip(result.iter_mut()) {
                *res = a_val + b_val;
            }
        }
    }
    
    /// Parallel element-wise multiplication for f32 slices.
    pub fn mul_f32_parallel(a: &[f32], b: &[f32], result: &mut [f32], config: &ParallelConfig) {
        assert_eq!(a.len(), b.len());
        assert_eq!(a.len(), result.len());
        
        if config.should_parallelize(a.len()) {
            let chunk_size = config.optimal_chunk_size(a.len());
            
            a.par_chunks(chunk_size)
                .zip(b.par_chunks(chunk_size))
                .zip(result.par_chunks_mut(chunk_size))
                .for_each(|((a_chunk, b_chunk), result_chunk)| {
                    for ((a_val, b_val), res) in a_chunk.iter().zip(b_chunk.iter()).zip(result_chunk.iter_mut()) {
                        *res = a_val * b_val;
                    }
                });
        } else {
            for ((a_val, b_val), res) in a.iter().zip(b.iter()).zip(result.iter_mut()) {
                *res = a_val * b_val;
            }
        }
    }
    
    /// Parallel scalar addition for f32 slices.
    pub fn add_scalar_f32_parallel(a: &[f32], scalar: f32, result: &mut [f32], config: &ParallelConfig) {
        assert_eq!(a.len(), result.len());
        
        if config.should_parallelize(a.len()) {
            let chunk_size = config.optimal_chunk_size(a.len());
            
            a.par_chunks(chunk_size)
                .zip(result.par_chunks_mut(chunk_size))
                .for_each(|(a_chunk, result_chunk)| {
                    for (a_val, res) in a_chunk.iter().zip(result_chunk.iter_mut()) {
                        *res = a_val + scalar;
                    }
                });
        } else {
            for (a_val, res) in a.iter().zip(result.iter_mut()) {
                *res = a_val + scalar;
            }
        }
    }
    
    /// Parallel scalar multiplication for f32 slices.
    pub fn mul_scalar_f32_parallel(a: &[f32], scalar: f32, result: &mut [f32], config: &ParallelConfig) {
        assert_eq!(a.len(), result.len());
        
        if config.should_parallelize(a.len()) {
            let chunk_size = config.optimal_chunk_size(a.len());
            
            a.par_chunks(chunk_size)
                .zip(result.par_chunks_mut(chunk_size))
                .for_each(|(a_chunk, result_chunk)| {
                    for (a_val, res) in a_chunk.iter().zip(result_chunk.iter_mut()) {
                        *res = a_val * scalar;
                    }
                });
        } else {
            for (a_val, res) in a.iter().zip(result.iter_mut()) {
                *res = a_val * scalar;
            }
        }
    }
    
    /// Parallel dot product for f32 slices.
    pub fn dot_product_f32_parallel(a: &[f32], b: &[f32], config: &ParallelConfig) -> f32 {
        assert_eq!(a.len(), b.len());
        
        if config.should_parallelize(a.len()) {
            let chunk_size = config.optimal_chunk_size(a.len());
            
            a.par_chunks(chunk_size)
                .zip(b.par_chunks(chunk_size))
                .map(|(a_chunk, b_chunk)| {
                    a_chunk.iter().zip(b_chunk.iter()).map(|(a_val, b_val)| a_val * b_val).sum::<f32>()
                })
                .sum()
        } else {
            a.iter().zip(b.iter()).map(|(a_val, b_val)| a_val * b_val).sum()
        }
    }
    
    /// Parallel sum reduction for f32 slices.
    pub fn sum_f32_parallel(a: &[f32], config: &ParallelConfig) -> f32 {
        if config.should_parallelize(a.len()) {
            let chunk_size = config.optimal_chunk_size(a.len());
            
            a.par_chunks(chunk_size)
                .map(|chunk| chunk.iter().sum::<f32>())
                .sum()
        } else {
            a.iter().sum()
        }
    }
    
    /// Parallel exponential function for f32 slices.
    pub fn exp_f32_parallel(a: &[f32], result: &mut [f32], config: &ParallelConfig) {
        assert_eq!(a.len(), result.len());
        
        if config.should_parallelize(a.len()) {
            let chunk_size = config.optimal_chunk_size(a.len());
            
            a.par_chunks(chunk_size)
                .zip(result.par_chunks_mut(chunk_size))
                .for_each(|(a_chunk, result_chunk)| {
                    for (a_val, res) in a_chunk.iter().zip(result_chunk.iter_mut()) {
                        *res = a_val.exp();
                    }
                });
        } else {
            for (a_val, res) in a.iter().zip(result.iter_mut()) {
                *res = a_val.exp();
            }
        }
    }
    
    /// Parallel sine function for f32 slices.
    pub fn sin_f32_parallel(a: &[f32], result: &mut [f32], config: &ParallelConfig) {
        assert_eq!(a.len(), result.len());
        
        if config.should_parallelize(a.len()) {
            let chunk_size = config.optimal_chunk_size(a.len());
            
            a.par_chunks(chunk_size)
                .zip(result.par_chunks_mut(chunk_size))
                .for_each(|(a_chunk, result_chunk)| {
                    for (a_val, res) in a_chunk.iter().zip(result_chunk.iter_mut()) {
                        *res = a_val.sin();
                    }
                });
        } else {
            for (a_val, res) in a.iter().zip(result.iter_mut()) {
                *res = a_val.sin();
            }
        }
    }
    
    /// Parallel cosine function for f32 slices.
    pub fn cos_f32_parallel(a: &[f32], result: &mut [f32], config: &ParallelConfig) {
        assert_eq!(a.len(), result.len());
        
        if config.should_parallelize(a.len()) {
            let chunk_size = config.optimal_chunk_size(a.len());
            
            a.par_chunks(chunk_size)
                .zip(result.par_chunks_mut(chunk_size))
                .for_each(|(a_chunk, result_chunk)| {
                    for (a_val, res) in a_chunk.iter().zip(result_chunk.iter_mut()) {
                        *res = a_val.cos();
                    }
                });
        } else {
            for (a_val, res) in a.iter().zip(result.iter_mut()) {
                *res = a_val.cos();
            }
        }
    }
    
    /// Parallel matrix multiplication for f32 matrices (row-major layout).
    pub fn matmul_f32_parallel(
        a: &[f32], a_rows: usize, a_cols: usize,
        b: &[f32], b_rows: usize, b_cols: usize,
        result: &mut [f32], config: &ParallelConfig
    ) {
        assert_eq!(a_cols, b_rows);
        assert_eq!(a.len(), a_rows * a_cols);
        assert_eq!(b.len(), b_rows * b_cols);
        assert_eq!(result.len(), a_rows * b_cols);
        
        if config.should_parallelize(a_rows * b_cols) {
            // Parallel over output rows
            result.par_chunks_mut(b_cols)
                .enumerate()
                .for_each(|(row_idx, result_row)| {
                    for col_idx in 0..b_cols {
                        let mut sum = 0.0;
                        for k in 0..a_cols {
                            sum += a[row_idx * a_cols + k] * b[k * b_cols + col_idx];
                        }
                        result_row[col_idx] = sum;
                    }
                });
        } else {
            // Sequential implementation
            for row_idx in 0..a_rows {
                for col_idx in 0..b_cols {
                    let mut sum = 0.0;
                    for k in 0..a_cols {
                        sum += a[row_idx * a_cols + k] * b[k * b_cols + col_idx];
                    }
                    result[row_idx * b_cols + col_idx] = sum;
                }
            }
        }
    }
}

/// Trait for parallel-optimized operations on numeric types.
pub trait ParallelOptimized<T: Numeric> {
    /// Parallel element-wise addition.
    fn parallel_add(&self, other: &[T], result: &mut [T], config: &ParallelConfig);
    
    /// Parallel element-wise multiplication.
    fn parallel_mul(&self, other: &[T], result: &mut [T], config: &ParallelConfig);
    
    /// Parallel scalar addition.
    fn parallel_add_scalar(&self, scalar: T, result: &mut [T], config: &ParallelConfig);
    
    /// Parallel scalar multiplication.
    fn parallel_mul_scalar(&self, scalar: T, result: &mut [T], config: &ParallelConfig);
    
    /// Parallel dot product.
    fn parallel_dot_product(&self, other: &[T], config: &ParallelConfig) -> T;
    
    /// Parallel sum reduction.
    fn parallel_sum(&self, config: &ParallelConfig) -> T;
}

impl ParallelOptimized<f32> for [f32] {
    fn parallel_add(&self, other: &[f32], result: &mut [f32], config: &ParallelConfig) {
        ParallelOps::add_f32_parallel(self, other, result, config);
    }
    
    fn parallel_mul(&self, other: &[f32], result: &mut [f32], config: &ParallelConfig) {
        ParallelOps::mul_f32_parallel(self, other, result, config);
    }
    
    fn parallel_add_scalar(&self, scalar: f32, result: &mut [f32], config: &ParallelConfig) {
        ParallelOps::add_scalar_f32_parallel(self, scalar, result, config);
    }
    
    fn parallel_mul_scalar(&self, scalar: f32, result: &mut [f32], config: &ParallelConfig) {
        ParallelOps::mul_scalar_f32_parallel(self, scalar, result, config);
    }
    
    fn parallel_dot_product(&self, other: &[f32], config: &ParallelConfig) -> f32 {
        ParallelOps::dot_product_f32_parallel(self, other, config)
    }
    
    fn parallel_sum(&self, config: &ParallelConfig) -> f32 {
        ParallelOps::sum_f32_parallel(self, config)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use approx::assert_relative_eq;

    #[test]
    fn test_parallel_config() {
        let config = ParallelConfig::default();
        assert_eq!(config.parallel_threshold, 10_000);
        assert_eq!(config.max_threads, None);
        assert_eq!(config.chunk_size, 1_000);

        // Test should_parallelize
        assert!(!config.should_parallelize(5_000));
        assert!(config.should_parallelize(15_000));

        // Test optimal_chunk_size
        let small_chunk = config.optimal_chunk_size(5_000);
        assert_eq!(small_chunk, 5_000); // Below threshold, use full size

        let large_chunk = config.optimal_chunk_size(50_000);
        assert!(large_chunk <= config.chunk_size);
    }

    #[test]
    fn test_parallel_add_f32() {
        let a = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0];
        let b = vec![8.0, 7.0, 6.0, 5.0, 4.0, 3.0, 2.0, 1.0];
        let mut result = vec![0.0; 8];
        let expected = vec![9.0, 9.0, 9.0, 9.0, 9.0, 9.0, 9.0, 9.0];

        let config = ParallelConfig::new(4, None, 2); // Low threshold for testing
        ParallelOps::add_f32_parallel(&a, &b, &mut result, &config);

        for (&res, &exp) in result.iter().zip(expected.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }
    }

    #[test]
    fn test_parallel_mul_f32() {
        let a = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0];
        let b = vec![2.0, 3.0, 4.0, 5.0, 6.0, 7.0];
        let mut result = vec![0.0; 6];
        let expected = vec![2.0, 6.0, 12.0, 20.0, 30.0, 42.0];

        let config = ParallelConfig::new(3, None, 2); // Low threshold for testing
        ParallelOps::mul_f32_parallel(&a, &b, &mut result, &config);

        for (&res, &exp) in result.iter().zip(expected.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }
    }

    #[test]
    fn test_parallel_add_scalar_f32() {
        let a = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0];
        let scalar = 10.0;
        let mut result = vec![0.0; 8];
        let expected = vec![11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0];

        let config = ParallelConfig::new(4, None, 2);
        ParallelOps::add_scalar_f32_parallel(&a, scalar, &mut result, &config);

        for (&res, &exp) in result.iter().zip(expected.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }
    }

    #[test]
    fn test_parallel_mul_scalar_f32() {
        let a = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0];
        let scalar = 3.0;
        let mut result = vec![0.0; 6];
        let expected = vec![3.0, 6.0, 9.0, 12.0, 15.0, 18.0];

        let config = ParallelConfig::new(3, None, 2);
        ParallelOps::mul_scalar_f32_parallel(&a, scalar, &mut result, &config);

        for (&res, &exp) in result.iter().zip(expected.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }
    }

    #[test]
    fn test_parallel_dot_product_f32() {
        let a = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0];
        let b = vec![6.0, 5.0, 4.0, 3.0, 2.0, 1.0];
        let expected = 56.0; // 1*6 + 2*5 + 3*4 + 4*3 + 5*2 + 6*1

        let config = ParallelConfig::new(3, None, 2);
        let result = ParallelOps::dot_product_f32_parallel(&a, &b, &config);

        assert_relative_eq!(result, expected, epsilon = 1e-6);
    }

    #[test]
    fn test_parallel_sum_f32() {
        let a = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0];
        let expected = 55.0; // Sum of 1 to 10

        let config = ParallelConfig::new(5, None, 3);
        let result = ParallelOps::sum_f32_parallel(&a, &config);

        assert_relative_eq!(result, expected, epsilon = 1e-6);
    }

    #[test]
    fn test_parallel_exp_f32() {
        let a = vec![0.0, 1.0, 2.0, -1.0, 0.5];
        let mut result = vec![0.0; 5];
        let expected: Vec<f32> = a.iter().map(|x| x.exp()).collect();

        let config = ParallelConfig::new(3, None, 2);
        ParallelOps::exp_f32_parallel(&a, &mut result, &config);

        for (&res, &exp) in result.iter().zip(expected.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }
    }

    #[test]
    fn test_parallel_sin_cos_f32() {
        let a = vec![0.0, std::f32::consts::PI / 2.0, std::f32::consts::PI, -std::f32::consts::PI / 2.0];
        let mut sin_result = vec![0.0; 4];
        let mut cos_result = vec![0.0; 4];
        let expected_sin: Vec<f32> = a.iter().map(|x| x.sin()).collect();
        let expected_cos: Vec<f32> = a.iter().map(|x| x.cos()).collect();

        let config = ParallelConfig::new(2, None, 2);
        ParallelOps::sin_f32_parallel(&a, &mut sin_result, &config);
        ParallelOps::cos_f32_parallel(&a, &mut cos_result, &config);

        for (&res, &exp) in sin_result.iter().zip(expected_sin.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }

        for (&res, &exp) in cos_result.iter().zip(expected_cos.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }
    }

    #[test]
    fn test_parallel_matmul_f32() {
        // Test 2x3 * 3x2 = 2x2 matrix multiplication
        let a = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0]; // 2x3 matrix
        let b = vec![7.0, 8.0, 9.0, 10.0, 11.0, 12.0]; // 3x2 matrix
        let mut result = vec![0.0; 4]; // 2x2 result

        // Expected result:
        // [1*7 + 2*9 + 3*11, 1*8 + 2*10 + 3*12] = [58, 64]
        // [4*7 + 5*9 + 6*11, 4*8 + 5*10 + 6*12] = [139, 154]
        let expected = vec![58.0, 64.0, 139.0, 154.0];

        let config = ParallelConfig::new(2, None, 1);
        ParallelOps::matmul_f32_parallel(&a, 2, 3, &b, 3, 2, &mut result, &config);

        for (&res, &exp) in result.iter().zip(expected.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }
    }

    #[test]
    fn test_parallel_trait_implementation() {
        let a = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0];
        let b = vec![8.0, 7.0, 6.0, 5.0, 4.0, 3.0, 2.0, 1.0];
        let mut result = vec![0.0; 8];

        let config = ParallelConfig::new(4, None, 2);

        // Test trait methods
        a.parallel_add(&b, &mut result, &config);
        let expected_add = vec![9.0, 9.0, 9.0, 9.0, 9.0, 9.0, 9.0, 9.0];
        for (&res, &exp) in result.iter().zip(expected_add.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }

        let dot_result = a.parallel_dot_product(&b, &config);
        assert_relative_eq!(dot_result, 120.0, epsilon = 1e-6); // 1*8 + 2*7 + ... + 8*1

        let sum_result = a.parallel_sum(&config);
        assert_relative_eq!(sum_result, 36.0, epsilon = 1e-6); // Sum of 1 to 8
    }

    #[test]
    fn test_sequential_fallback() {
        // Test that small arrays use sequential implementation
        let a = vec![1.0, 2.0, 3.0];
        let b = vec![4.0, 5.0, 6.0];
        let mut result = vec![0.0; 3];

        let config = ParallelConfig::default(); // High threshold
        ParallelOps::add_f32_parallel(&a, &b, &mut result, &config);

        let expected = vec![5.0, 7.0, 9.0];
        for (&res, &exp) in result.iter().zip(expected.iter()) {
            assert_relative_eq!(res, exp, epsilon = 1e-6);
        }
    }

    #[test]
    fn test_large_array_performance() {
        // Test with a larger array to verify parallel execution
        let size = 50_000;
        let a: Vec<f32> = (0..size).map(|i| i as f32).collect();
        let b: Vec<f32> = (0..size).map(|i| (size - i) as f32).collect();
        let mut result = vec![0.0; size];

        let config = ParallelConfig::default();
        ParallelOps::add_f32_parallel(&a, &b, &mut result, &config);

        // All results should be size (0+size, 1+(size-1), etc.)
        for &res in result.iter() {
            assert_relative_eq!(res, size as f32, epsilon = 1e-6);
        }
    }
}
