//! Demonstration of enhanced tensor operations.
//!
//! This example showcases the new mathematical functions, reduction operations,
//! and advanced tensor operations implemented in the Qilin inference engine.

use qilin_inference::tensor::{from_slice, zeros, ones, Shape, Tensor, TensorOps};
use qilin_inference::tensor::reduction::ReductionOps;
use qilin_inference::tensor::advanced::{AdvancedOps, Broadcasting};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🦄 Qilin Inference Engine - Enhanced Tensor Operations Demo");
    println!("============================================================\n");

    // Initialize the engine
    qilin_inference::init()?;

    // 1. Mathematical Functions Demo
    println!("1. Mathematical Functions");
    println!("-------------------------");
    
    let shape = Shape::new(vec![3, 3]);
    let data = vec![
        0.0, std::f32::consts::PI/4.0, std::f32::consts::PI/2.0,
        1.0, 2.0, 3.0,
        -1.0, 0.5, 2.5
    ];
    let tensor = from_slice(&data, &shape)?;
    
    println!("Original tensor:");
    print_tensor(&tensor);
    
    // Trigonometric functions
    let sin_result = tensor.sin()?;
    println!("sin(tensor):");
    print_tensor(&sin_result);
    
    let cos_result = tensor.cos()?;
    println!("cos(tensor):");
    print_tensor(&cos_result);
    
    // Exponential and logarithmic functions
    let positive_data = vec![1.0, 2.0, 3.0, 4.0];
    let positive_tensor = from_slice(&positive_data, &Shape::new(vec![2, 2]))?;
    
    let exp_result = positive_tensor.exp()?;
    println!("exp(positive_tensor):");
    print_tensor(&exp_result);
    
    let log_result = positive_tensor.log()?;
    println!("log(positive_tensor):");
    print_tensor(&log_result);
    
    // Power functions
    let pow_result = positive_tensor.pow_scalar(2.0)?;
    println!("positive_tensor^2:");
    print_tensor(&pow_result);
    
    // Rounding functions
    let decimal_data = vec![1.2, 1.7, -1.2, -1.7];
    let decimal_tensor = from_slice(&decimal_data, &Shape::new(vec![4]))?;
    
    let floor_result = decimal_tensor.floor()?;
    println!("floor([1.2, 1.7, -1.2, -1.7]):");
    print_tensor(&floor_result);
    
    let ceil_result = decimal_tensor.ceil()?;
    println!("ceil([1.2, 1.7, -1.2, -1.7]):");
    print_tensor(&ceil_result);
    
    // Clamp function
    let clamped = decimal_tensor.clamp(-1.0, 1.0)?;
    println!("clamp([1.2, 1.7, -1.2, -1.7], -1.0, 1.0):");
    print_tensor(&clamped);
    
    println!();

    // 2. Reduction Operations Demo
    println!("2. Reduction Operations");
    println!("-----------------------");
    
    let matrix_data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0];
    let matrix = from_slice(&matrix_data, &Shape::new(vec![2, 3]))?;
    
    println!("Matrix (2x3):");
    print_tensor(&matrix);
    
    // Sum operations
    let total_sum = matrix.sum_dim(&[], false)?;
    println!("Total sum: {}", total_sum.data()[0]);
    
    let mean_all = matrix.mean_dim(&[], false)?;
    println!("Mean of all elements: {}", mean_all.data()[0]);
    
    // Norm operations
    let l1_norm = matrix.norm_l1(None, false)?;
    println!("L1 norm: {}", l1_norm.data()[0]);
    
    let l2_norm = matrix.norm_l2(None, false)?;
    println!("L2 norm: {}", l2_norm.data()[0]);
    
    let frobenius_norm = matrix.norm_frobenius()?;
    println!("Frobenius norm: {}", frobenius_norm);
    
    // Variance and standard deviation
    let variance = matrix.var_dim(&[], false, false)?;
    println!("Population variance: {}", variance.data()[0]);
    
    let std_dev = matrix.std_dim(&[], false, false)?;
    println!("Population standard deviation: {}", std_dev.data()[0]);
    
    println!();

    // 3. Advanced Operations Demo
    println!("3. Advanced Operations");
    println!("----------------------");
    
    // Broadcasting demo
    let a = from_slice(&[1.0, 2.0], &Shape::new(vec![2, 1]))?;
    let b = from_slice(&[10.0, 20.0, 30.0], &Shape::new(vec![1, 3]))?;
    
    println!("Tensor A (2x1):");
    print_tensor(&a);
    
    println!("Tensor B (1x3):");
    print_tensor(&b);
    
    let target_shape = Shape::new(vec![2, 3]);
    let a_broadcasted = Broadcasting::broadcast_to(&a, &target_shape)?;
    let b_broadcasted = Broadcasting::broadcast_to(&b, &target_shape)?;
    
    println!("A broadcasted to (2x3):");
    print_tensor(&a_broadcasted);
    
    println!("B broadcasted to (2x3):");
    print_tensor(&b_broadcasted);
    
    let sum_broadcasted = a_broadcasted.add(&b_broadcasted)?;
    println!("A + B (broadcasted):");
    print_tensor(&sum_broadcasted);
    
    // Concatenation demo
    let tensor1 = from_slice(&[1.0, 2.0, 3.0, 4.0], &Shape::new(vec![2, 2]))?;
    let tensor2 = from_slice(&[5.0, 6.0, 7.0, 8.0], &Shape::new(vec![2, 2]))?;
    
    println!("Tensor 1 (2x2):");
    print_tensor(&tensor1);
    
    println!("Tensor 2 (2x2):");
    print_tensor(&tensor2);
    
    let cat_dim0 = qilin_inference::tensor::cpu::CpuTensor::cat(&[&tensor1, &tensor2], 0)?;
    println!("Concatenated along dimension 0 (4x2):");
    print_tensor(&cat_dim0);
    
    let cat_dim1 = qilin_inference::tensor::cpu::CpuTensor::cat(&[&tensor1, &tensor2], 1)?;
    println!("Concatenated along dimension 1 (2x4):");
    print_tensor(&cat_dim1);
    
    // Stacking demo
    let stack_dim0 = qilin_inference::tensor::cpu::CpuTensor::stack(&[&tensor1, &tensor2], 0)?;
    println!("Stacked along dimension 0 (2x2x2):");
    print_tensor(&stack_dim0);
    
    // Masked fill demo
    let data_to_mask = vec![1.0, 2.0, 3.0, 4.0];
    let mask_data = vec![1.0, 0.0, 1.0, 0.0]; // true, false, true, false
    
    let tensor_to_mask = from_slice(&data_to_mask, &Shape::new(vec![4]))?;
    let mask = from_slice(&mask_data, &Shape::new(vec![4]))?;
    
    println!("Original tensor: {:?}", tensor_to_mask.data());
    println!("Mask: {:?}", mask.data());
    
    let masked_filled = tensor_to_mask.masked_fill(&mask, 99.0)?;
    println!("After masked_fill with 99.0: {:?}", masked_filled.data());
    
    println!();

    // 4. Max/Min Operations Demo
    println!("4. Max/Min Operations");
    println!("---------------------");
    
    let test_data = vec![3.0, 1.0, 4.0, 1.0, 5.0, 9.0, 2.0, 6.0];
    let test_tensor = from_slice(&test_data, &Shape::new(vec![8]))?;
    
    println!("Test tensor: {:?}", test_tensor.data());
    
    let max_val = test_tensor.max(None, false)?;
    println!("Maximum value: {}", max_val.data()[0]);
    
    let min_val = test_tensor.min(None, false)?;
    println!("Minimum value: {}", min_val.data()[0]);
    
    println!("\n✅ All tensor operations completed successfully!");
    
    Ok(())
}

fn print_tensor<T: qilin_inference::tensor::Numeric + std::fmt::Display>(tensor: &qilin_inference::tensor::cpu::CpuTensor<T>) {
    let data = tensor.data();
    let shape = tensor.shape();
    
    match shape.rank() {
        1 => {
            print!("[");
            for (i, &val) in data.iter().enumerate() {
                if i > 0 { print!(", "); }
                print!("{:.3}", val);
            }
            println!("]");
        }
        2 => {
            let [rows, cols] = [shape.dims()[0], shape.dims()[1]];
            println!("[");
            for r in 0..rows {
                print!("  [");
                for c in 0..cols {
                    if c > 0 { print!(", "); }
                    print!("{:.3}", data[r * cols + c]);
                }
                println!("]");
            }
            println!("]");
        }
        3 => {
            let [d0, d1, d2] = [shape.dims()[0], shape.dims()[1], shape.dims()[2]];
            println!("[");
            for i in 0..d0 {
                println!("  [");
                for j in 0..d1 {
                    print!("    [");
                    for k in 0..d2 {
                        if k > 0 { print!(", "); }
                        print!("{:.3}", data[i * d1 * d2 + j * d2 + k]);
                    }
                    println!("]");
                }
                println!("  ]");
            }
            println!("]");
        }
        _ => {
            println!("Shape: {:?}, Data: {:?}", shape.dims(), data);
        }
    }
}
