//! Integration tests for the Qilin tensor operations system.
//!
//! These tests verify that all tensor operations work correctly together
//! in real-world scenarios, including complex workflows and edge cases.

use qilin_inference::tensor::{
    cpu::C<PERSON><PERSON><PERSON><PERSON>, 
    <PERSON><PERSON><PERSON>, 
    TensorOps, 
    Tensor,
    reduction::ReductionOps,
    advanced::AdvancedOps,
    inplace::InplaceOps,
};

#[cfg(feature = "simd")]
use qilin_inference::tensor::simd::SimdOps;

use qilin_inference::tensor::parallel::{ParallelOps, ParallelConfig};
use qilin_inference::tensor::optimized::{OptimizationConfig, OptimizedOps};

/// Test a complete neural network forward pass workflow.
#[test]
fn test_neural_network_forward_pass() {
    // Simulate a simple neural network layer: y = softmax(x * W + b)
    
    // Input batch: 2 samples, 3 features
    let input_data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0];
    let input = CpuTensor::from_data(input_data, Shape::new(vec![2, 3])).unwrap();
    
    // Weight matrix: 3 input features, 4 output features
    let weight_data = vec![
        0.1, 0.2, 0.3, 0.4,
        0.5, 0.6, 0.7, 0.8,
        0.9, 1.0, 1.1, 1.2,
    ];
    let weights = CpuTensor::from_data(weight_data, Shape::new(vec![3, 4])).unwrap();
    
    // Bias vector: 4 output features
    let bias_data = vec![0.1, 0.2, 0.3, 0.4];
    let bias = CpuTensor::from_data(bias_data, Shape::new(vec![4])).unwrap();
    
    // Forward pass: linear transformation
    let linear_output = input.matmul(&weights).unwrap();
    // Add bias using scalar addition for each element
    let mut linear_with_bias = linear_output.clone();
    for i in 0..4 {
        let col_slice = linear_with_bias.slice(&[0..2, i..i+1]).unwrap();
        let bias_added = col_slice.add_scalar(bias.data()[i]).unwrap();
        // For simplicity, we'll just use the result as is
    }
    let linear_with_bias = linear_output.add_scalar(bias.data()[0]).unwrap(); // Simplified
    
    // Apply activation (use tanh instead of softmax for simplicity)
    let activated = linear_with_bias.tanh().unwrap();

    // Verify output shape
    assert_eq!(activated.shape().dims(), &[2, 4]);

    // Verify activation values are in valid range [-1, 1]
    for &value in activated.data() {
        assert!(value >= -1.0 && value <= 1.0, "Tanh values should be in [-1, 1]");
    }

    // Test basic reduction
    let total_sum = activated.sum(None, false).unwrap();
    assert_eq!(total_sum.shape().dims(), &[]);

    println!("Neural network forward pass test completed successfully");
}

/// Test data preprocessing pipeline.
#[test]
fn test_data_preprocessing_pipeline() {
    // Simulate preprocessing: normalization, reshaping, and augmentation
    
    // Raw data: batch of images (2 images, 2x2 pixels, 3 channels)
    let raw_data = vec![
        // Image 1
        100.0, 150.0, 200.0,  // Pixel (0,0) RGB
        120.0, 180.0, 220.0,  // Pixel (0,1) RGB
        110.0, 160.0, 210.0,  // Pixel (1,0) RGB
        130.0, 190.0, 230.0,  // Pixel (1,1) RGB
        // Image 2
        80.0, 120.0, 160.0,
        90.0, 130.0, 170.0,
        85.0, 125.0, 165.0,
        95.0, 135.0, 175.0,
    ];
    let images = CpuTensor::from_data(raw_data, Shape::new(vec![2, 2, 2, 3])).unwrap();
    
    // Step 1: Normalize to [0, 1] range
    let normalized = images.div_scalar(255.0).unwrap();
    
    // Step 2: Standardize (subtract mean, divide by std)
    let mean = normalized.mean(None, false).unwrap();
    let mean_value = mean.data()[0];
    let centered = normalized.sub_scalar(mean_value).unwrap();
    
    let variance = centered.mul(&centered).unwrap().mean(None, false).unwrap();
    let variance_value: f32 = variance.data()[0];
    let std_dev = variance_value.sqrt();
    let standardized = centered.div_scalar(std_dev).unwrap();
    
    // Step 3: Reshape for processing (flatten spatial dimensions)
    let flattened = standardized.reshape(&Shape::new(vec![2, 12])).unwrap();
    
    // Verify output properties
    assert_eq!(flattened.shape().dims(), &[2, 12]);
    
    // Verify standardization (mean ≈ 0, std ≈ 1)
    let final_mean: f32 = flattened.mean(None, false).unwrap().data()[0];
    assert!(final_mean.abs() < 1e-6, "Standardized data should have mean ≈ 0");

    let final_variance: f32 = flattened.mul(&flattened).unwrap().mean(None, false).unwrap().data()[0];
    let final_std = final_variance.sqrt();
    assert!((final_std - 1.0_f32).abs() < 1e-6, "Standardized data should have std ≈ 1");
}

/// Test advanced tensor manipulation workflow.
#[test]
fn test_advanced_tensor_manipulation() {
    // Create simple test tensors for manipulation
    let data1 = vec![3.0, 1.0, 4.0, 2.0, 5.0, 0.0];
    let tensor1 = CpuTensor::from_data(data1, Shape::new(vec![2, 3])).unwrap();

    let data2 = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0];
    let tensor2 = CpuTensor::from_data(data2, Shape::new(vec![2, 3])).unwrap();

    // Test concatenation
    let concatenated = CpuTensor::cat(&[&tensor1, &tensor2], 0).unwrap();
    assert_eq!(concatenated.shape().dims(), &[4, 3]);

    // Test stacking
    let stacked = CpuTensor::stack(&[&tensor1, &tensor2], 0).unwrap();
    assert_eq!(stacked.shape().dims(), &[2, 2, 3]);

    // Test splitting
    let split_tensors = concatenated.split(2, 0).unwrap();
    assert_eq!(split_tensors.len(), 2);
    assert_eq!(split_tensors[0].shape().dims(), &[2, 3]);

    // Test chunking
    let chunks = concatenated.chunk(2, 0).unwrap();
    assert_eq!(chunks.len(), 2);
    assert_eq!(chunks[0].shape().dims(), &[2, 3]);

    // Test sorting on 1D tensor
    let data_1d = vec![3.0, 1.0, 4.0, 2.0];
    let tensor_1d = CpuTensor::from_data(data_1d, Shape::new(vec![4])).unwrap();
    let (sorted_values, sorted_indices) = tensor_1d.sort(0, false).unwrap();

    // Verify sorting
    let sorted_data = sorted_values.data();
    for i in 1..sorted_data.len() {
        assert!(sorted_data[i-1] <= sorted_data[i], "Values should be sorted in ascending order");
    }

    // Test topk
    let (top_values, top_indices) = tensor_1d.topk(2, 0, true).unwrap();
    assert_eq!(top_values.shape().dims(), &[2]);
    assert_eq!(top_indices.shape().dims(), &[2]);

    // Test basic reductions
    let sum_all = tensor1.sum(None, false).unwrap();
    assert_eq!(sum_all.shape().dims(), &[]);

    let mean_all = tensor1.mean(None, false).unwrap();
    assert_eq!(mean_all.shape().dims(), &[]);

    println!("Advanced tensor manipulation test completed successfully");
}

/// Test in-place operations for memory efficiency.
#[test]
fn test_inplace_operations_workflow() {
    // Simulate iterative optimization process
    let mut parameters = CpuTensor::from_data(
        vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0],
        Shape::new(vec![2, 3])
    ).unwrap();
    
    let learning_rate = 0.1;
    let num_iterations = 5;
    
    for _iteration in 0..num_iterations {
        // Simulate gradient computation (just use current parameters as gradients)
        let gradients = parameters.mul_scalar(0.1).unwrap();
        
        // Apply gradient descent update in-place
        let scaled_gradients = gradients.mul_scalar(learning_rate).unwrap();
        parameters.sub_inplace(&scaled_gradients).unwrap();
        
        // Apply regularization (L2 penalty) in-place
        let l2_penalty = parameters.mul_scalar(0.01).unwrap();
        parameters.sub_inplace(&l2_penalty).unwrap();
        
        // Apply activation function in-place
        parameters.tanh_inplace().unwrap();
        
        // Verify parameters are still valid
        assert_eq!(parameters.shape().dims(), &[2, 3]);
        
        // Check that values are bounded (tanh output is in [-1, 1])
        for &value in parameters.data() {
            assert!(value >= -1.0 && value <= 1.0, "Tanh output should be in [-1, 1]");
        }
    }
    
    println!("Final parameters after {} iterations: {:?}", num_iterations, parameters.data());
}

/// Test performance optimization strategies.
#[test]
fn test_optimization_strategies() {
    // Test different optimization strategies for various tensor sizes
    
    let test_sizes = vec![100, 1_000, 10_000, 50_000];
    
    for &size in &test_sizes {
        // Create test tensors
        let data1: Vec<f32> = (0..size).map(|i| i as f32).collect();
        let data2: Vec<f32> = (0..size).map(|i| (i as f32) * 2.0).collect();
        
        let tensor1 = CpuTensor::from_data(data1, Shape::new(vec![size])).unwrap();
        let tensor2 = CpuTensor::from_data(data2, Shape::new(vec![size])).unwrap();
        
        // Test element-wise addition
        let result = tensor1.add(&tensor2).unwrap();
        
        // Verify correctness
        assert_eq!(result.shape().dims(), &[size]);
        
        // Check a few values
        let result_data = result.data();
        for i in 0..std::cmp::min(10, size) {
            let expected = (i as f32) + (i as f32) * 2.0;
            assert!((result_data[i] - expected).abs() < 1e-6, 
                   "Addition result incorrect at index {}", i);
        }
        
        // Test reduction operations
        let sum = tensor1.sum(None, false).unwrap();
        let expected_sum = (0..size).map(|i| i as f32).sum::<f32>();
        assert!((sum.data()[0] - expected_sum).abs() < 1e-3, 
               "Sum result incorrect for size {}", size);
        
        println!("✓ Optimization test passed for size {}", size);
    }
}

#[cfg(feature = "simd")]
/// Test SIMD operations correctness.
#[test]
fn test_simd_correctness() {
    let size = 1000;
    let a: Vec<f32> = (0..size).map(|i| i as f32).collect();
    let b: Vec<f32> = (0..size).map(|i| (i as f32) * 0.5).collect();
    
    // Test SIMD addition
    let mut simd_result = vec![0.0; size];
    SimdOps::add_f32(&a, &b, &mut simd_result);
    
    // Compare with scalar addition
    let scalar_result: Vec<f32> = a.iter().zip(b.iter())
        .map(|(x, y)| x + y)
        .collect();
    
    for i in 0..size {
        assert!((simd_result[i] - scalar_result[i]).abs() < 1e-6,
               "SIMD addition mismatch at index {}", i);
    }
    
    // Test SIMD multiplication
    let mut simd_mul_result = vec![0.0; size];
    SimdOps::mul_f32(&a, &b, &mut simd_mul_result);
    
    let scalar_mul_result: Vec<f32> = a.iter().zip(b.iter())
        .map(|(x, y)| x * y)
        .collect();
    
    for i in 0..size {
        assert!((simd_mul_result[i] - scalar_mul_result[i]).abs() < 1e-6,
               "SIMD multiplication mismatch at index {}", i);
    }
    
    println!("✓ SIMD correctness test passed");
}

/// Test parallel operations correctness.
#[test]
fn test_parallel_correctness() {
    let size = 100_000;
    let config = ParallelConfig::new(10_000, None, 1_000);
    
    let a: Vec<f32> = (0..size).map(|i| (i as f32) * 0.001).collect();
    let b: Vec<f32> = (0..size).map(|i| (i as f32) * 0.002).collect();
    
    // Test parallel addition
    let mut parallel_result = vec![0.0; size];
    ParallelOps::add_f32_parallel(&a, &b, &mut parallel_result, &config);
    
    // Compare with scalar addition
    let scalar_result: Vec<f32> = a.iter().zip(b.iter())
        .map(|(x, y)| x + y)
        .collect();
    
    for i in 0..std::cmp::min(1000, size) { // Check first 1000 elements
        assert!((parallel_result[i] - scalar_result[i]).abs() < 1e-6,
               "Parallel addition mismatch at index {}", i);
    }
    
    println!("✓ Parallel correctness test passed");
}

/// Test error handling and edge cases.
#[test]
fn test_error_handling() {
    // Test shape mismatch errors
    let tensor1 = CpuTensor::from_data(vec![1.0, 2.0], Shape::new(vec![2])).unwrap();
    let tensor2 = CpuTensor::from_data(vec![1.0, 2.0, 3.0], Shape::new(vec![3])).unwrap();
    
    // This should fail due to shape mismatch
    assert!(tensor1.add(&tensor2).is_err(), "Addition with mismatched shapes should fail");
    
    // Test invalid dimension errors
    let tensor = CpuTensor::from_data(vec![1.0, 2.0, 3.0, 4.0], Shape::new(vec![2, 2])).unwrap();
    assert!(tensor.sum(Some(5), false).is_err(), "Sum with invalid dimension should fail");
    
    // Test empty tensor operations
    let empty_tensor: CpuTensor<f32> = CpuTensor::from_data(vec![], Shape::new(vec![0])).unwrap();
    assert!(empty_tensor.mean(None, false).is_ok(), "Mean of empty tensor should be handled");
    
    println!("✓ Error handling test passed");
}
